#!/usr/bin/env node

/**
 * 国际化测试脚本
 * 验证翻译文件的完整性和一致性
 */

const fs = require('fs');
const path = require('path');

// 读取翻译文件
function loadTranslations() {
  const enPath = path.join(__dirname, '../locales/en.json');
  const zhPath = path.join(__dirname, '../locales/zh.json');
  
  try {
    const en = JSON.parse(fs.readFileSync(enPath, 'utf8'));
    const zh = JSON.parse(fs.readFileSync(zhPath, 'utf8'));
    return { en, zh };
  } catch (error) {
    console.error('❌ 无法读取翻译文件:', error.message);
    process.exit(1);
  }
}

// 获取对象的所有键路径
function getKeyPaths(obj, prefix = '') {
  const paths = [];
  
  for (const key in obj) {
    const currentPath = prefix ? `${prefix}.${key}` : key;
    
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      paths.push(...getKeyPaths(obj[key], currentPath));
    } else {
      paths.push(currentPath);
    }
  }
  
  return paths;
}

// 检查翻译完整性
function checkTranslationCompleteness(en, zh) {
  const enKeys = getKeyPaths(en);
  const zhKeys = getKeyPaths(zh);
  
  const missingInZh = enKeys.filter(key => !zhKeys.includes(key));
  const missingInEn = zhKeys.filter(key => !enKeys.includes(key));
  
  console.log('🔍 检查翻译完整性...\n');
  
  if (missingInZh.length > 0) {
    console.log('❌ 中文翻译中缺失的键:');
    missingInZh.forEach(key => console.log(`   - ${key}`));
    console.log();
  }
  
  if (missingInEn.length > 0) {
    console.log('❌ 英文翻译中缺失的键:');
    missingInEn.forEach(key => console.log(`   - ${key}`));
    console.log();
  }
  
  if (missingInZh.length === 0 && missingInEn.length === 0) {
    console.log('✅ 所有翻译键都存在于两种语言中');
  }
  
  return {
    totalKeys: enKeys.length,
    missingInZh: missingInZh.length,
    missingInEn: missingInEn.length
  };
}

// 检查空值
function checkEmptyValues(translations) {
  console.log('\n🔍 检查空值...\n');
  
  Object.entries(translations).forEach(([lang, data]) => {
    const keys = getKeyPaths(data);
    const emptyKeys = [];
    
    keys.forEach(key => {
      const value = key.split('.').reduce((obj, k) => obj?.[k], data);
      if (!value || value.trim() === '') {
        emptyKeys.push(key);
      }
    });
    
    if (emptyKeys.length > 0) {
      console.log(`❌ ${lang.toUpperCase()} 中的空值:`)
      emptyKeys.forEach(key => console.log(`   - ${key}`));
    } else {
      console.log(`✅ ${lang.toUpperCase()} 中没有空值`);
    }
  });
}

// 生成统计报告
function generateReport(stats) {
  console.log('\n📊 统计报告');
  console.log('='.repeat(40));
  console.log(`总翻译键数量: ${stats.totalKeys}`);
  console.log(`中文缺失键数: ${stats.missingInZh}`);
  console.log(`英文缺失键数: ${stats.missingInEn}`);
  console.log(`完整性: ${((stats.totalKeys - Math.max(stats.missingInZh, stats.missingInEn)) / stats.totalKeys * 100).toFixed(1)}%`);
}

// 主函数
function main() {
  console.log('🌐 CryptoPay 国际化测试\n');
  
  const translations = loadTranslations();
  const stats = checkTranslationCompleteness(translations.en, translations.zh);
  
  checkEmptyValues(translations);
  generateReport(stats);
  
  if (stats.missingInZh === 0 && stats.missingInEn === 0) {
    console.log('\n🎉 所有测试通过！国际化配置正确。');
    process.exit(0);
  } else {
    console.log('\n⚠️  发现问题，请修复后重新测试。');
    process.exit(1);
  }
}

// 运行测试
main();
