# CryptoPay 国际化功能说明

## 概述

本项目已成功集成国际化（i18n）功能，支持英文（English）和中文（简体中文）两种语言。

## 功能特性

### ✅ 已实现的功能

1. **语言切换组件** - 用户可以在页面右上角切换语言
2. **自动语言检测** - 根据浏览器语言自动设置默认语言
3. **本地存储** - 用户的语言选择会保存在localStorage中
4. **实时切换** - 无需刷新页面即可切换语言
5. **响应式设计** - 语言切换器在移动端和桌面端都有良好的显示效果

### 🌐 支持的语言

- **English (en)** - 英文
- **中文 (zh)** - 简体中文

## 文件结构

```
├── locales/                    # 语言文件目录
│   ├── en.json                # 英文翻译
│   └── zh.json                # 中文翻译
├── contexts/
│   └── LanguageContext.tsx   # 语言上下文和Hook
├── components/
│   ├── LanguageSwitcher.tsx   # 语言切换组件
│   └── LoadingFallback.tsx    # 国际化加载组件
└── app/
    ├── layout.tsx             # 根布局（包含LanguageProvider）
    └── i18n-demo/             # 国际化演示页面
        └── page.tsx
```

## 使用方法

### 1. 在组件中使用翻译

```tsx
'use client';

import { useLanguage } from '../contexts/LanguageContext';

export default function MyComponent() {
  const { t } = useLanguage();

  return (
    <div>
      <h1>{t('navigation.home')}</h1>
      <p>{t('hero.subtitle')}</p>
    </div>
  );
}
```

### 2. 添加新的翻译

在 `locales/en.json` 和 `locales/zh.json` 中添加对应的键值对：

```json
// locales/en.json
{
  "mySection": {
    "title": "My Title",
    "description": "My Description"
  }
}

// locales/zh.json
{
  "mySection": {
    "title": "我的标题",
    "description": "我的描述"
  }
}
```

### 3. 使用嵌套键

```tsx
const title = t('mySection.title');
const description = t('mySection.description');
```

### 4. 语言切换

```tsx
import { useLanguage } from '../contexts/LanguageContext';

export default function MyComponent() {
  const { language, setLanguage } = useLanguage();

  return (
    <div>
      <button onClick={() => setLanguage('en')}>English</button>
      <button onClick={() => setLanguage('zh')}>中文</button>
      <p>Current language: {language}</p>
    </div>
  );
}
```

## 已国际化的组件

### ✅ 完成
- Header（导航栏）
- Footer（页脚）
- HeroSection（英雄区域）
- LanguageSwitcher（语言切换器）
- LoadingFallback（加载组件）

### 🔄 待完成
- FeaturesSection（功能介绍）
- HowItWorksSection（工作原理）
- ComplianceSection（合规性）
- CurrencySection（货币支持）
- DeveloperSection（开发者）
- Dashboard相关组件

## 演示页面

访问 `/i18n-demo` 页面可以查看国际化功能的演示，包括：
- 翻译示例展示
- 语言切换测试
- 实时内容更新

## 技术实现

### 语言上下文（LanguageContext）

- 使用React Context API管理全局语言状态
- 提供 `useLanguage` Hook 供组件使用
- 自动检测浏览器语言
- 本地存储用户语言偏好

### 翻译函数（t函数）

- 支持嵌套键访问（如 `hero.stats.activeUsers`）
- 键不存在时返回键名本身，便于调试
- 控制台警告缺失的翻译键

### 语言切换器

- 美观的下拉菜单设计
- 国旗图标显示
- 响应式布局
- 平滑动画效果

## 最佳实践

1. **翻译键命名**：使用有意义的嵌套结构，如 `section.subsection.item`
2. **组件标记**：需要翻译的组件添加 `'use client'` 指令
3. **加载状态**：使用 `LoadingFallback` 组件提供一致的加载体验
4. **错误处理**：翻译键不存在时有合理的降级处理

## 扩展语言

要添加新语言（如法语）：

1. 在 `locales/` 目录下创建 `fr.json`
2. 在 `LanguageContext.tsx` 中添加语言类型和翻译导入
3. 在 `LanguageSwitcher.tsx` 中添加语言选项
4. 更新类型定义

## 注意事项

- 项目使用 `output: "export"` 配置，不能使用Next.js内置的i18n功能
- 所有需要翻译的组件必须是客户端组件（'use client'）
- 语言切换会立即生效，无需页面刷新
- 翻译文件使用JSON格式，注意语法正确性

## 测试

1. 启动开发服务器：`npm run dev`
2. 访问 `http://localhost:3000`
3. 点击右上角的语言切换器
4. 观察页面内容的实时变化
5. 访问 `/i18n-demo` 查看详细演示

## 贡献

添加新翻译或改进现有翻译时，请确保：
- 英文和中文翻译保持一致的结构
- 翻译准确且符合上下文
- 遵循现有的命名约定
- 测试翻译在实际页面中的显示效果
