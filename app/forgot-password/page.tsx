'use client';

import { useState } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function ForgotPassword() {
  const [step, setStep] = useState(1);
  const [email, setEmail] = useState('');
  const [code, setCode] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Email submitted:', email);
    setStep(2);
  };

  const handleCodeSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Code submitted:', code);
    setStep(3);
  };

  const handlePasswordReset = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Password reset:', { newPassword, confirmPassword });
    setStep(4);
  };

  const steps = [
    { title: '输入邮箱', description: '输入您的注册邮箱地址', color: 'from-blue-500 to-blue-600' },
    { title: '验证码', description: '输入发送到您邮箱的验证码', color: 'from-green-500 to-green-600' },
    { title: '重置密码', description: '设置您的新密码', color: 'from-purple-500 to-purple-600' },
    { title: '完成', description: '密码重置成功', color: 'from-emerald-500 to-emerald-600' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      {/* 升级的装饰性背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-[500px] h-[500px] bg-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-32 w-[700px] h-[700px] bg-purple-400/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/3 w-96 h-96 bg-indigo-400/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
        <div className="absolute top-1/3 right-1/6 w-48 h-48 bg-pink-400/15 rounded-full blur-2xl animate-pulse delay-3000"></div>
      </div>
      
      <Header />
      
      <main className="py-20 relative">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-8 py-4 rounded-full text-sm font-medium mb-10 shadow-2xl">
              <i className="ri-lock-unlock-line mr-3 text-lg"></i>
              密码重置
            </div>
            <h1 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8">
              重置密码
            </h1>
            <p className="text-gray-600 text-2xl max-w-2xl mx-auto">
              按照以下步骤重置您的账户密码
            </p>
          </div>
          
          {/* 升级的步骤指示器 */}
          <div className="mb-20">
            <div className="flex justify-between items-center max-w-5xl mx-auto">
              {steps.map((stepItem, index) => (
                <div key={index} className="flex items-center flex-1">
                  <div className="flex flex-col items-center text-center">
                    <div className={`flex items-center justify-center w-20 h-20 rounded-3xl font-bold text-xl transition-all duration-300 shadow-2xl ${
                      index + 1 < step ? 'bg-gradient-to-r from-green-500 to-green-600 text-white' :
                      index + 1 === step ? `bg-gradient-to-r ${stepItem.color} text-white` :
                      'bg-gray-200 text-gray-600'
                    }`}>
                      {index + 1 < step ? (
                        <i className="ri-check-line text-3xl"></i>
                      ) : (
                        index + 1
                      )}
                    </div>
                    <div className="mt-6">
                      <div className={`text-lg font-medium ${
                        index + 1 <= step ? 'text-gray-900' : 'text-gray-500'
                      }`}>
                        {stepItem.title}
                      </div>
                      <div className="text-sm text-gray-500 mt-2">
                        {stepItem.description}
                      </div>
                    </div>
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`flex-1 h-2 mx-12 rounded-full ${
                      index + 1 < step ? 'bg-gradient-to-r from-green-500 to-green-600' : 'bg-gray-200'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          <div className="max-w-lg mx-auto">
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative bg-white/95 backdrop-blur-sm rounded-3xl p-12 border border-gray-100 shadow-2xl">
                {step === 1 && (
                  <form onSubmit={handleEmailSubmit} className="space-y-10">
                    <div className="text-center mb-10">
                      <div className="w-28 h-28 bg-gradient-to-r from-blue-500 to-blue-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
                        <i className="ri-mail-line text-white text-4xl"></i>
                      </div>
                      <h2 className="text-4xl font-bold text-gray-900 mb-4">输入您的邮箱</h2>
                      <p className="text-gray-600 text-xl">我们将向您发送重置密码的验证码</p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-4">
                        <i className="ri-mail-line mr-3 text-blue-500 text-lg"></i>
                        邮箱地址
                      </label>
                      <input
                        type="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full px-6 py-5 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                        placeholder="请输入您的邮箱地址"
                        required
                      />
                    </div>
                    
                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-5 px-8 rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap"
                    >
                      <i className="ri-send-plane-line mr-3"></i>
                      发送验证码
                    </button>
                  </form>
                )}
                
                {step === 2 && (
                  <form onSubmit={handleCodeSubmit} className="space-y-10">
                    <div className="text-center mb-10">
                      <div className="w-28 h-28 bg-gradient-to-r from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
                        <i className="ri-mail-check-line text-white text-4xl"></i>
                      </div>
                      <h2 className="text-4xl font-bold text-gray-900 mb-4">验证您的邮箱</h2>
                      <p className="text-gray-600 text-xl">
                        我们已向 <span className="font-medium text-blue-600">{email}</span> 发送了验证码
                      </p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-4">
                        <i className="ri-key-line mr-3 text-green-500 text-lg"></i>
                        验证码
                      </label>
                      <input
                        type="text"
                        value={code}
                        onChange={(e) => setCode(e.target.value)}
                        className="w-full px-6 py-5 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-center text-3xl tracking-widest bg-white/90 backdrop-blur-sm"
                        placeholder="000000"
                        maxLength={6}
                        required
                      />
                    </div>
                    
                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-5 px-8 rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap"
                    >
                      <i className="ri-check-line mr-3"></i>
                      验证并继续
                    </button>
                    
                    <div className="text-center">
                      <button
                        type="button"
                        className="text-blue-600 hover:text-blue-800 text-sm transition-colors duration-300 font-medium"
                        onClick={() => setStep(1)}
                      >
                        <i className="ri-refresh-line mr-2"></i>
                        重新发送验证码
                      </button>
                    </div>
                  </form>
                )}
                
                {step === 3 && (
                  <form onSubmit={handlePasswordReset} className="space-y-10">
                    <div className="text-center mb-10">
                      <div className="w-28 h-28 bg-gradient-to-r from-purple-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
                        <i className="ri-lock-password-line text-white text-4xl"></i>
                      </div>
                      <h2 className="text-4xl font-bold text-gray-900 mb-4">设置新密码</h2>
                      <p className="text-gray-600 text-xl">请设置一个安全的新密码</p>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-4">
                        <i className="ri-lock-line mr-3 text-purple-500 text-lg"></i>
                        新密码
                      </label>
                      <div className="relative">
                        <input
                          type={showPassword ? 'text' : 'password'}
                          value={newPassword}
                          onChange={(e) => setNewPassword(e.target.value)}
                          className="w-full px-6 py-5 pr-16 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                          placeholder="请输入新密码"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-300"
                        >
                          <i className={`${showPassword ? 'ri-eye-off-line' : 'ri-eye-line'} text-xl`}></i>
                        </button>
                      </div>
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-4">
                        <i className="ri-lock-line mr-3 text-orange-500 text-lg"></i>
                        确认新密码
                      </label>
                      <div className="relative">
                        <input
                          type={showConfirmPassword ? 'text' : 'password'}
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className="w-full px-6 py-5 pr-16 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                          placeholder="请再次输入新密码"
                          required
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-300"
                        >
                          <i className={`${showConfirmPassword ? 'ri-eye-off-line' : 'ri-eye-line'} text-xl`}></i>
                        </button>
                      </div>
                    </div>
                    
                    <button
                      type="submit"
                      className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-5 px-8 rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap"
                    >
                      <i className="ri-lock-unlock-line mr-3"></i>
                      重置密码
                    </button>
                  </form>
                )}
                
                {step === 4 && (
                  <div className="text-center space-y-10">
                    <div className="w-28 h-28 bg-gradient-to-r from-green-500 to-green-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
                      <i className="ri-check-line text-white text-5xl"></i>
                    </div>
                    <h2 className="text-4xl font-bold text-gray-900 mb-4">密码重置成功</h2>
                    <p className="text-gray-600 mb-12 text-xl">
                      您的密码已成功重置，现在可以使用新密码登录
                    </p>
                    
                    <Link
                      href="/login"
                      className="inline-block bg-gradient-to-r from-blue-600 to-purple-600 text-white py-5 px-10 rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap"
                    >
                      <i className="ri-login-circle-line mr-3"></i>
                      立即登录
                    </Link>
                  </div>
                )}
                
                {step < 4 && (
                  <div className="mt-10 text-center">
                    <Link href="/login" className="text-blue-600 hover:text-blue-800 text-sm transition-colors duration-300 font-medium">
                      <i className="ri-arrow-left-line mr-2"></i>
                      返回登录页面
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}