'use client';

import { useState } from 'react';

export default function PricingFAQ() {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const faqs = [
    {
      question: '费用是如何计算的？',
      answer: '我们的费用基于交易金额的百分比收取，不同支付方式和交易量级别有不同的费率。加密货币支付通常为1.5%-2.5%，信用卡支付为3.2%+$0.30。'
    },
    {
      question: '是否有最低费用要求？',
      answer: '加密货币交易没有最低费用要求，按比例收取。信用卡交易有最低$0.30的固定费用。银行转账根据转账类型有不同的最低费用。'
    },
    {
      question: '批量折扣是如何工作的？',
      answer: '批量折扣基于您的月交易量自动计算。交易量越大，享受的折扣越多。折扣会在下个月的费率中体现，无需手动申请。'
    },
    {
      question: '是否有设置费用或月费？',
      answer: '我们不收取任何设置费用或月费。您只需要为实际发生的交易支付手续费，完全按使用付费。'
    },
    {
      question: '汇率是如何确定的？',
      answer: '我们的汇率基于多个主要交易所的实时价格，加上较小的汇率点差。点差通常为0.1%-0.5%，确保价格公平且有竞争力。'
    },
    {
      question: '企业用户可以获得定制定价吗？',
      answer: '当然可以。月交易量超过100万美元的企业用户可以联系我们的销售团队，获取定制的费率方案和专属服务。'
    },
    {
      question: '费用什么时候扣除？',
      answer: '费用在交易成功完成时自动扣除。您收到的金额已经是扣除手续费后的净额，无需额外操作。'
    },
    {
      question: '如何查看费用明细？',
      answer: '您可以在仪表板中查看详细的交易记录和费用明细。我们还提供月度和年度费用报告，帮助您更好地管理成本。'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            费用相关问题
          </h2>
          <p className="text-xl text-gray-600">
            关于定价和费用的常见问题解答
          </p>
        </div>
        
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-xl shadow-md overflow-hidden">
              <button
                className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <h3 className="text-lg font-semibold text-gray-900">
                  {faq.question}
                </h3>
                <i className={`ri-arrow-${openIndex === index ? 'up' : 'down'}-s-line text-gray-500 text-xl`}></i>
              </button>
              {openIndex === index && (
                <div className="px-6 pb-4">
                  <p className="text-gray-600 leading-relaxed">
                    {faq.answer}
                  </p>
                </div>
              )}
            </div>
          ))}
        </div>
        
        <div className="mt-12 bg-blue-600 rounded-xl p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">
            还有其他问题？
          </h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            我们的客服团队随时为您解答定价相关的任何问题
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200 whitespace-nowrap cursor-pointer">
              <i className="ri-customer-service-line w-5 h-5 flex items-center justify-center mr-2 inline-flex"></i>
              在线客服
            </button>
            <button className="border border-white text-white px-6 py-3 rounded-lg font-medium hover:bg-white/10 transition-colors duration-200 whitespace-nowrap cursor-pointer">
              <i className="ri-phone-line w-5 h-5 flex items-center justify-center mr-2 inline-flex"></i>
              电话咨询
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}