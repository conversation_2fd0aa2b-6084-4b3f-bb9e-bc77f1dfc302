'use client';

export default function VolumeDiscounts() {
  const discountTiers = [
    {
      tier: 'Tier 1',
      volume: '$0 - $10,000',
      discount: '0%',
      rate: '2.5%',
      color: 'bg-gray-100 text-gray-900'
    },
    {
      tier: 'Tier 2',
      volume: '$10,001 - $50,000',
      discount: '5%',
      rate: '2.375%',
      color: 'bg-blue-100 text-blue-900'
    },
    {
      tier: 'Tier 3',
      volume: '$50,001 - $100,000',
      discount: '10%',
      rate: '2.25%',
      color: 'bg-green-100 text-green-900'
    },
    {
      tier: 'Tier 4',
      volume: '$100,001 - $500,000',
      discount: '15%',
      rate: '2.125%',
      color: 'bg-purple-100 text-purple-900'
    },
    {
      tier: 'Tier 5',
      volume: '$500,001 - $1,000,000',
      discount: '20%',
      rate: '2.0%',
      color: 'bg-yellow-100 text-yellow-900'
    },
    {
      tier: 'Tier 6',
      volume: '$1,000,000+',
      discount: '25%',
      rate: '1.875%',
      color: 'bg-red-100 text-red-900'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            批量折扣阶梯
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            根据月交易量自动享受更优惠的费率
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {discountTiers.map((tier, index) => (
            <div key={index} className="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className={`inline-block px-3 py-1 rounded-full text-sm font-medium mb-4 ${tier.color}`}>
                {tier.tier}
              </div>
              <div className="mb-4">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  月交易量
                </h3>
                <p className="text-xl font-bold text-blue-600">
                  {tier.volume}
                </p>
              </div>
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-700 mb-1">折扣率</h4>
                <p className="text-2xl font-bold text-green-600">
                  {tier.discount}
                </p>
              </div>
              <div className="mb-6">
                <h4 className="text-sm font-medium text-gray-700 mb-1">实际费率</h4>
                <p className="text-2xl font-bold text-gray-900">
                  {tier.rate}
                </p>
              </div>
              <div className="text-sm text-gray-600">
                <div className="flex items-center justify-between mb-2">
                  <span>每月节省</span>
                  <span className="font-medium">
                    {index === 0 ? '$0' : `$${(parseFloat(tier.volume.split(' - $')[1]?.replace(',', '') || '0') * 0.025 * (parseFloat(tier.discount.replace('%', '')) / 100)).toFixed(0)}`}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span>年节省</span>
                  <span className="font-medium text-green-600">
                    {index === 0 ? '$0' : `$${(parseFloat(tier.volume.split(' - $')[1]?.replace(',', '') || '0') * 0.025 * (parseFloat(tier.discount.replace('%', '')) / 100) * 12).toFixed(0)}`}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-12 bg-blue-600 rounded-xl p-8 text-white text-center">
          <h3 className="text-2xl font-bold mb-4">
            企业定制方案
          </h3>
          <p className="text-blue-100 mb-6 max-w-2xl mx-auto">
            月交易量超过100万美元？联系我们的企业销售团队，获取专属定制方案和更优惠的费率。
          </p>
          <button className="bg-white text-blue-600 px-6 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors duration-200 whitespace-nowrap cursor-pointer">
            <i className="ri-customer-service-line w-5 h-5 flex items-center justify-center mr-2 inline-flex"></i>
            联系企业销售
          </button>
        </div>
      </div>
    </section>
  );
}