'use client';

export default function TransparencySection() {
  const transparencyFeatures = [
    {
      icon: 'ri-eye-line',
      title: '无隐藏费用',
      description: '所有费用结构透明公开，绝无隐藏收费项目。'
    },
    {
      icon: 'ri-calculator-line',
      title: '实时费用计算',
      description: '交易前实时显示确切费用，让您心中有数。'
    },
    {
      icon: 'ri-file-list-line',
      title: '详细账单',
      description: '提供详细的交易记录和费用明细报告。'
    },
    {
      icon: 'ri-shield-check-line',
      title: '费率保护',
      description: '合同期内费率锁定，不会随意调整。'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            透明度承诺
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们承诺提供完全透明的定价结构，让您的每一笔交易都清晰明了
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {transparencyFeatures.map((feature, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className={`${feature.icon} text-blue-600 text-2xl`}></i>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
        
        <div className="bg-gray-50 rounded-xl p-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">
            费用对比
          </h3>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-semibold text-gray-900">服务商</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-900">加密货币费率</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-900">信用卡费率</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-900">设置费用</th>
                  <th className="text-center py-3 px-4 font-semibold text-gray-900">月费</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-gray-200 bg-blue-50">
                  <td className="py-3 px-4 font-medium text-blue-600">CryptoPay</td>
                  <td className="py-3 px-4 text-center text-blue-600 font-medium">1.5% - 2.5%</td>
                  <td className="py-3 px-4 text-center text-blue-600 font-medium">3.2% + $0.30</td>
                  <td className="py-3 px-4 text-center text-blue-600 font-medium">免费</td>
                  <td className="py-3 px-4 text-center text-blue-600 font-medium">免费</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 text-gray-600">竞争对手 A</td>
                  <td className="py-3 px-4 text-center text-gray-600">2.0% - 3.0%</td>
                  <td className="py-3 px-4 text-center text-gray-600">3.5% + $0.30</td>
                  <td className="py-3 px-4 text-center text-gray-600">$500</td>
                  <td className="py-3 px-4 text-center text-gray-600">$99</td>
                </tr>
                <tr className="border-b border-gray-200">
                  <td className="py-3 px-4 text-gray-600">竞争对手 B</td>
                  <td className="py-3 px-4 text-center text-gray-600">2.5% - 3.5%</td>
                  <td className="py-3 px-4 text-center text-gray-600">3.8% + $0.35</td>
                  <td className="py-3 px-4 text-center text-gray-600">$1000</td>
                  <td className="py-3 px-4 text-center text-gray-600">$199</td>
                </tr>
                <tr>
                  <td className="py-3 px-4 text-gray-600">竞争对手 C</td>
                  <td className="py-3 px-4 text-center text-gray-600">3.0% - 4.0%</td>
                  <td className="py-3 px-4 text-center text-gray-600">4.0% + $0.40</td>
                  <td className="py-3 px-4 text-center text-gray-600">$750</td>
                  <td className="py-3 px-4 text-center text-gray-600">$149</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </section>
  );
}