'use client';

import { useState } from 'react';

export default function PricingToggle() {
  const [activeTab, setActiveTab] = useState('crypto');

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            选择支付方式
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            根据不同的支付方式，我们提供具有竞争力的费率结构
          </p>
        </div>
        
        <div className="flex justify-center mb-12">
          <div className="bg-white rounded-full p-1 shadow-md">
            <button
              onClick={() => setActiveTab('crypto')}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                activeTab === 'crypto'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              加密货币支付
            </button>
            <button
              onClick={() => setActiveTab('bank')}
              className={`px-6 py-3 rounded-full font-medium transition-all duration-200 ${
                activeTab === 'bank'
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              银行转账
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {activeTab === 'crypto' ? (
            <>
              <div className="bg-white rounded-xl p-8 shadow-md">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">基础版</h3>
                  <p className="text-gray-600">适合小型企业</p>
                </div>
                <div className="text-center mb-6">
                  <span className="text-4xl font-bold text-blue-600">2.5%</span>
                  <span className="text-gray-600 ml-2">每笔交易</span>
                </div>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    月交易额 ≤ $10,000
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    基础API支持
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    邮件客服支持
                  </li>
                </ul>
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  开始使用
                </button>
              </div>
              
              <div className="bg-white rounded-xl p-8 shadow-md border-2 border-blue-600 relative">
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">推荐</span>
                </div>
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">专业版</h3>
                  <p className="text-gray-600">适合中型企业</p>
                </div>
                <div className="text-center mb-6">
                  <span className="text-4xl font-bold text-blue-600">2.0%</span>
                  <span className="text-gray-600 ml-2">每笔交易</span>
                </div>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    月交易额 $10,000 - $100,000
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    完整API + SDK
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    24/7技术支持
                  </li>
                </ul>
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  开始使用
                </button>
              </div>
              
              <div className="bg-white rounded-xl p-8 shadow-md">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">企业版</h3>
                  <p className="text-gray-600">适合大型企业</p>
                </div>
                <div className="text-center mb-6">
                  <span className="text-4xl font-bold text-blue-600">1.5%</span>
                  <span className="text-gray-600 ml-2">每笔交易</span>
                </div>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    月交易额 {'>'} $100,000
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    专属客户经理
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    定制化解决方案
                  </li>
                </ul>
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  联系销售
                </button>
              </div>
            </>
          ) : (
            <>
              <div className="bg-white rounded-xl p-8 shadow-md">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">标准转账</h3>
                  <p className="text-gray-600">1-3个工作日</p>
                </div>
                <div className="text-center mb-6">
                  <span className="text-4xl font-bold text-blue-600">1.0%</span>
                  <span className="text-gray-600 ml-2">每笔交易</span>
                </div>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    最低费用 $5
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    支持主要银行
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    自动对账
                  </li>
                </ul>
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  开始使用
                </button>
              </div>
              
              <div className="bg-white rounded-xl p-8 shadow-md border-2 border-blue-600 relative">
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">推荐</span>
                </div>
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">快速转账</h3>
                  <p className="text-gray-600">2-4小时</p>
                </div>
                <div className="text-center mb-6">
                  <span className="text-4xl font-bold text-blue-600">1.5%</span>
                  <span className="text-gray-600 ml-2">每笔交易</span>
                </div>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    最低费用 $10
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    优先处理
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    实时通知
                  </li>
                </ul>
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  开始使用
                </button>
              </div>
              
              <div className="bg-white rounded-xl p-8 shadow-md">
                <div className="text-center mb-6">
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">即时转账</h3>
                  <p className="text-gray-600">实时到账</p>
                </div>
                <div className="text-center mb-6">
                  <span className="text-4xl font-bold text-blue-600">2.0%</span>
                  <span className="text-gray-600 ml-2">每笔交易</span>
                </div>
                <ul className="space-y-3 mb-8">
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    最低费用 $15
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    24/7即时处理
                  </li>
                  <li className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    高级安全保障
                  </li>
                </ul>
                <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  开始使用
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </section>
  );
}