
'use client';

import { useState } from 'react';

export default function FeeStructure() {
  const [activeTab, setActiveTab] = useState('individual');

  const individualFees = [
    {
      currency: 'BTC',
      name: 'Bitcoin',
      logo: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
      buyFee: '0.5%',
      sellFee: '0.5%',
      withdrawFee: '0.0005 BTC',
      color: 'from-orange-500 to-orange-600',
      bgColor: 'from-orange-50 to-orange-100'
    },
    {
      currency: 'ETH',
      name: 'Ethereum',
      logo: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
      buyFee: '0.5%',
      sellFee: '0.5%',
      withdrawFee: '0.005 ETH',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100'
    },
    {
      currency: 'USDT',
      name: 'Tether',
      logo: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
      buyFee: '0.1%',
      sellFee: '0.1%',
      withdrawFee: '1 USDT',
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-50 to-green-100'
    },
    {
      currency: 'BNB',
      name: 'Binance Coin',
      logo: 'https://cryptologos.cc/logos/bnb-bnb-logo.png',
      buyFee: '0.25%',
      sellFee: '0.25%',
      withdrawFee: '0.001 BNB',
      color: 'from-yellow-500 to-yellow-600',
      bgColor: 'from-yellow-50 to-yellow-100'
    },
    {
      currency: 'ADA',
      name: 'Cardano',
      logo: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
      buyFee: '0.5%',
      sellFee: '0.5%',
      withdrawFee: '0.5 ADA',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100'
    },
    {
      currency: 'DOT',
      name: 'Polkadot',
      logo: 'https://cryptologos.cc/logos/polkadot-new-dot-logo.png',
      buyFee: '0.5%',
      sellFee: '0.5%',
      withdrawFee: '0.1 DOT',
      color: 'from-pink-500 to-pink-600',
      bgColor: 'from-pink-50 to-pink-100'
    }
  ];

  const businessFees = [
    {
      tier: 'Starter',
      volume: '< $100K',
      buyFee: '0.3%',
      sellFee: '0.3%',
      apiCalls: '10,000/月',
      support: '邮件支持',
      color: 'from-blue-500 to-blue-600'
    },
    {
      tier: 'Professional',
      volume: '$100K - $1M',
      buyFee: '0.2%',
      sellFee: '0.2%',
      apiCalls: '100,000/月',
      support: '优先支持',
      color: 'from-purple-500 to-purple-600'
    },
    {
      tier: 'Enterprise',
      volume: '> $1M',
      buyFee: '0.1%',
      sellFee: '0.1%',
      apiCalls: '无限制',
      support: '专属客服',
      color: 'from-green-500 to-green-600'
    }
  ];

  return (
    <section className="py-24 bg-gradient-to-br from-white via-gray-50 to-blue-50 relative overflow-hidden">
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_70%)]"></div>
      <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(139,92,246,0.1),transparent_70%)]"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <i className="ri-price-tag-line mr-2"></i>
            费用结构
          </div>
          <h2 className="text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
            透明的费用结构
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            简单透明的费用结构，无隐藏费用
          </p>
        </div>

        <div className="flex justify-center mb-12">
          <div className="bg-white/70 backdrop-blur-sm rounded-2xl p-2 shadow-lg border border-gray-100">
            <div className="flex space-x-1">
              <button
                onClick={() => setActiveTab('individual')}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap ${
                  activeTab === 'individual'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <i className="ri-user-line mr-2"></i>
                个人用户
              </button>
              <button
                onClick={() => setActiveTab('business')}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 whitespace-nowrap ${
                  activeTab === 'business'
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                <i className="ri-building-line mr-2"></i>
                企业用户
              </button>
            </div>
          </div>
        </div>

        {activeTab === 'individual' ? (
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl border border-gray-100 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="bg-gradient-to-r from-gray-50 to-gray-100 border-b border-gray-200">
                    <th className="text-left py-6 px-8 text-lg font-bold text-gray-900">加密货币</th>
                    <th className="text-center py-6 px-6 text-lg font-bold text-gray-900">购买费用</th>
                    <th className="text-center py-6 px-6 text-lg font-bold text-gray-900">出售费用</th>
                    <th className="text-center py-6 px-6 text-lg font-bold text-gray-900">提现费用</th>
                    <th className="text-center py-6 px-6 text-lg font-bold text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {individualFees.map((fee, index) => (
                    <tr key={index} className="border-b border-gray-100 hover:bg-gray-50/50 transition-colors duration-200">
                      <td className="py-6 px-8">
                        <div className="flex items-center">
                          <div className={`w-12 h-12 bg-gradient-to-r ${fee.bgColor} rounded-xl flex items-center justify-center mr-4 shadow-lg`}>
                            <img
                              src={fee.logo}
                              alt={`${fee.name} logo`}
                              className="w-8 h-8 object-contain"
                            />
                          </div>
                          <div>
                            <div className="text-lg font-bold text-gray-900">{fee.currency}</div>
                            <div className="text-sm text-gray-600">{fee.name}</div>
                          </div>
                        </div>
                      </td>
                      <td className="text-center py-6 px-6">
                        <span className="text-lg font-medium text-gray-900">{fee.buyFee}</span>
                      </td>
                      <td className="text-center py-6 px-6">
                        <span className="text-lg font-medium text-gray-900">{fee.sellFee}</span>
                      </td>
                      <td className="text-center py-6 px-6">
                        <span className="text-lg font-medium text-gray-900">{fee.withdrawFee}</span>
                      </td>
                      <td className="text-center py-6 px-6">
                        <button className={`bg-gradient-to-r ${fee.color} text-white px-6 py-2 rounded-xl hover:shadow-lg transition-all duration-200 text-sm font-medium whitespace-nowrap`}>
                          立即交易
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {businessFees.map((tier, index) => (
              <div key={index} className="group relative">
                <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl blur-xl"></div>
                <div className="relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg shadow-gray-900/5 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 border border-gray-100">
                  <div className="text-center mb-8">
                    <div className={`w-16 h-16 bg-gradient-to-r ${tier.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                      <i className="ri-vip-crown-line text-2xl text-white"></i>
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.tier}</h3>
                    <p className="text-gray-600">{tier.volume}</p>
                  </div>

                  <div className="space-y-4 mb-8">
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">购买费用</span>
                      <span className="font-bold text-gray-900">{tier.buyFee}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">出售费用</span>
                      <span className="font-bold text-gray-900">{tier.sellFee}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">API调用</span>
                      <span className="font-bold text-gray-900">{tier.apiCalls}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-gray-600">支持服务</span>
                      <span className="font-bold text-gray-900">{tier.support}</span>
                    </div>
                  </div>

                  <button className={`w-full bg-gradient-to-r ${tier.color} text-white py-3 px-6 rounded-2xl hover:shadow-lg transition-all duration-200 font-medium whitespace-nowrap`}>
                    <i className="ri-arrow-right-line mr-2"></i>
                    选择方案
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </section>
  );
}
