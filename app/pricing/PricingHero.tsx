
'use client';

import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

export default function PricingHero() {
  const { t } = useLanguage();
  const [billingPeriod, setBillingPeriod] = useState('monthly');

  const plans = [
    {
      name: '基础版',
      price: billingPeriod === 'monthly' ? 29 : 290,
      description: '适合小型企业和初创公司',
      features: [
        '每月最多1000笔交易',
        '5种主流加密货币',
        '基础API访问',
        '邮件客服支持',
        '基础数据分析'
      ],
      color: 'from-blue-500 to-blue-600',
      popular: false
    },
    {
      name: '专业版',
      price: billingPeriod === 'monthly' ? 99 : 990,
      description: '适合成长型企业',
      features: [
        '每月最多10000笔交易',
        '20+种加密货币',
        '完整API访问',
        '24/7客服支持',
        '高级数据分析',
        '自定义集成',
        '优先处理'
      ],
      color: 'from-purple-500 to-purple-600',
      popular: true
    },
    {
      name: '企业版',
      price: billingPeriod === 'monthly' ? 299 : 2990,
      description: '适合大型企业',
      features: [
        '无限制交易',
        '全部加密货币',
        '专属API',
        '专属客户经理',
        '企业级分析',
        '专属集成支持',
        '最高优先级',
        '合规咨询'
      ],
      color: 'from-green-500 to-green-600',
      popular: false
    }
  ];

  return (
    <section className="py-28 bg-white relative overflow-hidden">
      {/* 高级装饰背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-green-200/30 to-blue-200/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-purple-200/30 to-pink-200/30 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>
      
      {/* 动态网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.05)_1px,transparent_1px)] bg-[size:80px_80px]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8 shadow-lg">
            <i className="ri-price-tag-line mr-2"></i>
            {t('pricing.hero.tagline')}
          </div>

          <h1 className="text-7xl md:text-8xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8 leading-tight">
            {t('pricing.hero.title')}
          </h1>

          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            {t('pricing.hero.subtitle')}
          </p>
          
          {/* 计费周期切换 */}
          <div className="inline-flex items-center bg-gray-100 rounded-2xl p-2 mb-16">
            <button
              onClick={() => setBillingPeriod('monthly')}
              className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                billingPeriod === 'monthly'
                  ? 'bg-white text-gray-900 shadow-lg'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              按月计费
            </button>
            <button
              onClick={() => setBillingPeriod('yearly')}
              className={`px-6 py-3 rounded-xl text-sm font-medium transition-all duration-300 ${
                billingPeriod === 'yearly'
                  ? 'bg-white text-gray-900 shadow-lg'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              按年计费
              <span className="ml-2 bg-gradient-to-r from-green-500 to-green-600 text-white px-2 py-1 rounded-full text-xs">
                省20%
              </span>
            </button>
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-20">
          {plans.map((plan, index) => (
            <div key={index} className={`group relative ${plan.popular ? 'scale-105' : ''}`}>
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-gradient-to-r from-purple-500 to-purple-600 text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg">
                  <i className="ri-star-line mr-1"></i>
                  最受欢迎
                </div>
              )}
              
              <div className={`absolute inset-0 bg-gradient-to-r ${plan.color} rounded-3xl blur-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300`}></div>
              
              <div className={`relative bg-white rounded-3xl p-8 shadow-2xl border-2 ${
                plan.popular ? 'border-purple-200' : 'border-gray-100'
              } hover:shadow-3xl transition-all duration-300 hover:-translate-y-1`}>
                
                <div className="text-center mb-8">
                  <div className={`w-20 h-20 bg-gradient-to-r ${plan.color} rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg`}>
                    <i className="ri-vip-crown-line text-white text-3xl"></i>
                  </div>
                  
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                  <p className="text-gray-600 mb-6">{plan.description}</p>
                  
                  <div className="text-center">
                    <div className="flex items-baseline justify-center mb-2">
                      <span className={`text-5xl font-bold bg-gradient-to-r ${plan.color} bg-clip-text text-transparent`}>
                        ${plan.price}
                      </span>
                      <span className="text-gray-600 ml-2">
                        /{billingPeriod === 'monthly' ? '月' : '年'}
                      </span>
                    </div>
                    {billingPeriod === 'yearly' && (
                      <div className="text-sm text-green-600 font-medium">
                        节省 ${(plan.price * 12 - plan.price * 10) / 10} /月
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="space-y-4 mb-8">
                  {plan.features.map((feature, featureIndex) => (
                    <div key={featureIndex} className="flex items-center">
                      <div className={`w-6 h-6 bg-gradient-to-r ${plan.color} rounded-full flex items-center justify-center mr-3`}>
                        <i className="ri-check-line text-white text-sm"></i>
                      </div>
                      <span className="text-gray-700">{feature}</span>
                    </div>
                  ))}
                </div>
                
                <button className={`w-full py-4 px-6 rounded-xl font-semibold text-lg transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer ${
                  plan.popular
                    ? `bg-gradient-to-r ${plan.color} text-white shadow-2xl hover:shadow-3xl`
                    : `border-2 border-gray-200 text-gray-700 hover:bg-gray-50`
                }`}>
                  {plan.popular ? '开始免费试用' : '选择方案'}
                </button>
              </div>
            </div>
          ))}
        </div>
        
        {/* 附加信息 */}
        <div className="text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-12 border border-gray-200">
          <h3 className="text-3xl font-bold text-gray-900 mb-6">
            需要定制方案？
          </h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            我们为大型企业和特殊需求提供量身定制的解决方案。
            联系我们的销售团队获取专属报价。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer">
              <i className="ri-customer-service-line mr-2"></i>
              联系销售
            </button>
            <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 whitespace-nowrap cursor-pointer">
              <i className="ri-calendar-line mr-2"></i>
              预约演示
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
