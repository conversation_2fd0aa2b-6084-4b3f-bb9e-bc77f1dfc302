'use client';

import { Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import HeroSection from './HeroSection';
import FeaturesSection from './FeaturesSection';
import HowItWorksSection from './HowItWorksSection';
import CurrencySection from './CurrencySection';
import ComplianceSection from './ComplianceSection';
import DeveloperSection from './DeveloperSection';
import LoadingFallback from '../components/LoadingFallback';

export default function Home() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense fallback={<LoadingFallback icon="ri-home-4-line" />}>
          <HeroSection />
          <FeaturesSection />
          <HowItWorksSection />
          <CurrencySection />
          <ComplianceSection />
          <DeveloperSection />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}