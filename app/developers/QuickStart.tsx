'use client';

import { useState } from 'react';

export default function QuickStart() {
  const [activeStep, setActiveStep] = useState(0);

  const steps = [
    {
      title: '获取 API 密钥',
      description: '在控制台中创建您的 API 密钥',
      code: `// 在控制台中创建 API 密钥
const apiKey = "pk_live_your_api_key_here";`
    },
    {
      title: '安装 SDK',
      description: '选择您喜欢的编程语言安装 SDK',
      code: `# Node.js
npm install @cryptopay/node-sdk

# Python
pip install cryptopay-python

# PHP
composer require cryptopay/php-sdk`
    },
    {
      title: '创建支付',
      description: '使用 API 创建您的第一个支付请求',
      code: `// JavaScript
const cryptoPay = new CryptoPay(apiKey);

const payment = await cryptoPay.payments.create({
  amount: 1000,
  currency: 'USD',
  cryptoCurrency: 'BTC',
  returnUrl: 'https://your-site.com/success'
});

console.log(payment.paymentUrl);`
    },
    {
      title: '处理回调',
      description: '设置 Webhook 处理支付状态更新',
      code: `// Express.js 示例
app.post('/webhook', (req, res) => {
  const signature = req.headers['x-cryptopay-signature'];
  const payload = req.body;
  
  if (cryptoPay.webhooks.verify(payload, signature)) {
    // 处理支付状态更新
    console.log('Payment status:', payload.status);
  }
  
  res.sendStatus(200);
});`
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            快速开始
          </h2>
          <p className="text-xl text-gray-600">
            四个简单步骤，快速集成加密货币支付
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div className="space-y-4">
            {steps.map((step, index) => (
              <div
                key={index}
                className={`p-6 rounded-xl border-2 cursor-pointer transition-all duration-200 ${
                  activeStep === index
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setActiveStep(index)}
              >
                <div className="flex items-center mb-2">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 text-sm font-bold ${
                    activeStep === index
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}>
                    {index + 1}
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900">
                    {step.title}
                  </h3>
                </div>
                <p className="text-gray-600 ml-11">
                  {step.description}
                </p>
              </div>
            ))}
          </div>
          
          <div className="lg:sticky lg:top-8">
            <div className="bg-gray-900 rounded-xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-white font-semibold">
                  {steps[activeStep].title}
                </h3>
                <button className="text-gray-400 hover:text-white transition-colors">
                  <i className="ri-file-copy-line"></i>
                </button>
              </div>
              <pre className="text-green-400 text-sm overflow-x-auto">
                <code>{steps[activeStep].code}</code>
              </pre>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}