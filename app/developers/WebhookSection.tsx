'use client';

export default function WebhookSection() {
  const webhookEvents = [
    {
      event: 'payment.created',
      description: '支付请求创建时触发',
      example: {
        event: 'payment.created',
        data: {
          id: 'pay_123456',
          status: 'pending',
          amount: 1000,
          currency: 'USD'
        }
      }
    },
    {
      event: 'payment.confirmed',
      description: '支付确认时触发',
      example: {
        event: 'payment.confirmed',
        data: {
          id: 'pay_123456',
          status: 'confirmed',
          amount: 1000,
          currency: 'USD',
          crypto_amount: 0.022,
          crypto_currency: 'BTC'
        }
      }
    },
    {
      event: 'payment.failed',
      description: '支付失败时触发',
      example: {
        event: 'payment.failed',
        data: {
          id: 'pay_123456',
          status: 'failed',
          error: 'insufficient_funds'
        }
      }
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Webhook 集成
          </h2>
          <p className="text-xl text-gray-600">
            实时接收支付状态更新，确保您的应用与支付状态同步
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          <div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-6">
              支持的事件类型
            </h3>
            <div className="space-y-4">
              {webhookEvents.map((webhook, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="text-lg font-medium text-gray-900 mb-1">
                        {webhook.event}
                      </h4>
                      <p className="text-gray-600">{webhook.description}</p>
                    </div>
                    <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                      实时
                    </span>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700">示例数据</span>
                      <button className="text-gray-400 hover:text-gray-600">
                        <i className="ri-file-copy-line"></i>
                      </button>
                    </div>
                    <pre className="text-sm text-gray-800 overflow-x-auto">
                      <code>{JSON.stringify(webhook.example, null, 2)}</code>
                    </pre>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          <div>
            <h3 className="text-2xl font-semibold text-gray-900 mb-6">
              处理 Webhook
            </h3>
            
            <div className="space-y-6">
              <div className="bg-gray-900 rounded-xl p-6">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-white font-semibold">Express.js 示例</h4>
                  <button className="text-gray-400 hover:text-white">
                    <i className="ri-file-copy-line"></i>
                  </button>
                </div>
                <pre className="text-green-400 text-sm overflow-x-auto">
                  <code>{`const express = require('express');
const crypto = require('crypto');

app.post('/webhook', (req, res) => {
  const signature = req.headers['x-cryptopay-signature'];
  const payload = JSON.stringify(req.body);
  
  // 验证签名
  const expectedSignature = crypto
    .createHmac('sha256', webhookSecret)
    .update(payload)
    .digest('hex');
  
  if (signature === expectedSignature) {
    const event = req.body;
    
    switch (event.event) {
      case 'payment.confirmed':
        // 处理支付确认
        console.log('Payment confirmed:', event.data);
        break;
      case 'payment.failed':
        // 处理支付失败
        console.log('Payment failed:', event.data);
        break;
    }
    
    res.sendStatus(200);
  } else {
    res.sendStatus(400);
  }
});`}</code>
                </pre>
              </div>
              
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                <div className="flex items-start">
                  <i className="ri-shield-check-line text-yellow-600 text-xl mr-3 mt-1"></i>
                  <div>
                    <h4 className="font-semibold text-yellow-800 mb-2">安全最佳实践</h4>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• 始终验证 Webhook 签名</li>
                      <li>• 使用 HTTPS 端点接收 Webhook</li>
                      <li>• 实现幂等性处理重复事件</li>
                      <li>• 设置合理的超时时间</li>
                    </ul>
                  </div>
                </div>
              </div>
              
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <div className="flex items-start">
                  <i className="ri-information-line text-blue-600 text-xl mr-3 mt-1"></i>
                  <div>
                    <h4 className="font-semibold text-blue-800 mb-2">Webhook 配置</h4>
                    <p className="text-sm text-blue-700 mb-3">
                      在控制台中配置您的 Webhook 端点和需要接收的事件类型
                    </p>
                    <button className="bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm whitespace-nowrap cursor-pointer">
                      配置 Webhook
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}