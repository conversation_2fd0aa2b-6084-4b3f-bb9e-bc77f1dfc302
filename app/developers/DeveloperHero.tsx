
'use client';

import { useState } from 'react';

export default function DeveloperHero() {
  const [activeTab, setActiveTab] = useState(0);

  const codeExamples = [
    {
      title: 'REST API',
      language: 'javascript',
      code: `// 创建支付
const payment = await fetch('https://api.example.com/v1/payments', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    amount: '100.00',
    currency: 'BTC',
    description: '产品购买'
  })
});

const result = await payment.json();
console.log('Payment ID:', result.id);`
    },
    {
      title: 'Node.js SDK',
      language: 'javascript',
      code: `const CryptoPayment = require('@example/crypto-payment');

const client = new CryptoPayment({
  apiKey: 'YOUR_API_KEY',
  environment: 'production'
});

// 创建支付
const payment = await client.payments.create({
  amount: '100.00',
  currency: 'BTC',
  description: '产品购买'
});

console.log('Payment created:', payment.id);`
    },
    {
      title: 'Webhook',
      language: 'javascript',
      code: `// Express.js webhook 处理
app.post('/webhook', express.raw({type: 'application/json'}), (req, res) => {
  const signature = req.headers['x-signature'];
  const payload = req.body;
  
  // 验证签名
  if (verifySignature(payload, signature)) {
    const event = JSON.parse(payload);
    
    if (event.type === 'payment.completed') {
      // 处理支付完成事件
      console.log('Payment completed:', event.data.id);
    }
  }
  
  res.status(200).send('OK');
});`
    }
  ];

  const features = [
    {
      title: '简单集成',
      description: '5分钟内完成API集成',
      icon: 'ri-code-line',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: '多语言SDK',
      description: '支持10+编程语言',
      icon: 'ri-global-line',
      color: 'from-green-500 to-green-600'
    },
    {
      title: '完整文档',
      description: '详细的API文档和示例',
      icon: 'ri-book-open-line',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: '沙盒环境',
      description: '免费的测试环境',
      icon: 'ri-flask-line',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  return (
    <section className="py-28 bg-white relative overflow-hidden">
      {/* 高级装饰背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-green-200/30 to-blue-200/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-purple-200/30 to-pink-200/30 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>
      
      {/* 动态网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.05)_1px,transparent_1px)] bg-[size:80px_80px]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* 左侧内容 */}
          <div className="space-y-8">
            <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium shadow-lg">
              <i className="ri-terminal-line mr-2"></i>
              为开发者而生
            </div>
            
            <div className="space-y-6">
              <h1 className="text-7xl md:text-8xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent leading-tight">
                开发者
                <br />
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  API平台
                </span>
              </h1>
              
              <p className="text-xl text-gray-600 max-w-2xl leading-relaxed">
                强大的API和SDK让您轻松集成加密货币支付功能。
                完整的文档、示例代码和测试环境助您快速上手。
              </p>
            </div>
            
            {/* 核心特性 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <div key={index} className="flex items-center p-4 bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl border border-gray-200">
                  <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mr-4`}>
                    <i className={`${feature.icon} text-white text-xl`}></i>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                    <p className="text-gray-600 text-sm">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
            
            {/* 行动按钮 */}
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer">
                <i className="ri-rocket-line mr-2"></i>
                开始开发
              </button>
              <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-300 whitespace-nowrap cursor-pointer">
                <i className="ri-book-open-line mr-2"></i>
                查看文档
              </button>
            </div>
            
            {/* 快速统计 */}
            <div className="grid grid-cols-3 gap-6 pt-8">
              <div className="text-center">
                <div className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-2">
                  99.9%
                </div>
                <div className="text-gray-600 text-sm">API稳定性</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-2">
                  50ms
                </div>
                <div className="text-gray-600 text-sm">平均响应时间</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent mb-2">
                  24/7
                </div>
                <div className="text-gray-600 text-sm">技术支持</div>
              </div>
            </div>
          </div>
          
          {/* 右侧代码示例 */}
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-xl"></div>
            <div className="relative bg-white rounded-3xl p-8 shadow-2xl border border-gray-100">
              
              {/* 代码选项卡 */}
              <div className="flex space-x-4 mb-6">
                {codeExamples.map((example, index) => (
                  <button
                    key={index}
                    onClick={() => setActiveTab(index)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      activeTab === index
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {example.title}
                  </button>
                ))}
              </div>
              
              {/* 代码内容 */}
              <div className="bg-gray-900 rounded-2xl p-6 text-sm">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                    <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  </div>
                  <span className="text-gray-400 text-xs">{codeExamples[activeTab].language}</span>
                </div>
                <pre className="text-gray-300 overflow-x-auto">
                  <code>{codeExamples[activeTab].code}</code>
                </pre>
              </div>
              
              {/* 复制按钮 */}
              <div className="flex justify-end mt-4">
                <button className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 cursor-pointer">
                  <i className="ri-file-copy-line mr-2"></i>
                  复制代码
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* 底部快速开始 */}
        <div className="mt-20 text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-12 border border-gray-200">
          <h3 className="text-4xl font-bold text-gray-900 mb-6">
            5分钟快速开始
          </h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            获取API密钥，查看文档，开始您的第一个集成。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer">
              <i className="ri-key-line mr-2"></i>
              获取API密钥
            </button>
            <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 whitespace-nowrap cursor-pointer">
              <i className="ri-play-circle-line mr-2"></i>
              交互式教程
            </button>
          </div>
        </div>
      </div>
    </section>
  );
}
