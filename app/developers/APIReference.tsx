'use client';

import { useState, useMemo, useCallback } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

// TypeScript interfaces for better type safety
interface APIEndpoint {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE';
  endpoint: string;
  description: string;
  params: string[];
}

interface APIEndpoints {
  payments: APIEndpoint[];
  wallets: APIEndpoint[];
  webhooks: APIEndpoint[];
}

interface Tab {
  id: keyof APIEndpoints;
  name: string;
  icon: string;
}

export default function APIReference() {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState<keyof APIEndpoints>('payments');

  // Memoized API endpoints with internationalization
  const apiEndpoints = useMemo<APIEndpoints>(() => ({
    payments: [
      {
        method: 'POST' as const,
        endpoint: '/v1/payments',
        description: t('developers.api.payments.create.description') || '创建新的支付请求',
        params: ['amount', 'currency', 'crypto_currency', 'return_url']
      },
      {
        method: 'GET' as const,
        endpoint: '/v1/payments/{id}',
        description: t('developers.api.payments.get.description') || '获取支付详情',
        params: ['id']
      },
      {
        method: 'GET' as const,
        endpoint: '/v1/payments',
        description: t('developers.api.payments.list.description') || '获取支付列表',
        params: ['limit', 'offset', 'status']
      }
    ],
    wallets: [
      {
        method: 'GET' as const,
        endpoint: '/v1/wallets',
        description: t('developers.api.wallets.list.description') || '获取钱包列表',
        params: ['currency']
      },
      {
        method: 'GET' as const,
        endpoint: '/v1/wallets/{id}/balance',
        description: t('developers.api.wallets.balance.description') || '获取钱包余额',
        params: ['id']
      },
      {
        method: 'POST' as const,
        endpoint: '/v1/wallets/transfer',
        description: t('developers.api.wallets.transfer.description') || '钱包间转账',
        params: ['from_wallet', 'to_wallet', 'amount']
      }
    ],
    webhooks: [
      {
        method: 'POST' as const,
        endpoint: '/v1/webhooks',
        description: t('developers.api.webhooks.create.description') || '创建 Webhook',
        params: ['url', 'events']
      },
      {
        method: 'GET' as const,
        endpoint: '/v1/webhooks',
        description: t('developers.api.webhooks.list.description') || '获取 Webhook 列表',
        params: ['limit', 'offset']
      },
      {
        method: 'DELETE' as const,
        endpoint: '/v1/webhooks/{id}',
        description: t('developers.api.webhooks.delete.description') || '删除 Webhook',
        params: ['id']
      }
    ]
  }), [t]);

  // Memoized tabs with internationalization
  const tabs = useMemo<Tab[]>(() => [
    {
      id: 'payments',
      name: t('developers.api.tabs.payments') || '支付',
      icon: 'ri-money-dollar-circle-line'
    },
    {
      id: 'wallets',
      name: t('developers.api.tabs.wallets') || '钱包',
      icon: 'ri-wallet-line'
    },
    {
      id: 'webhooks',
      name: t('developers.api.tabs.webhooks') || 'Webhooks',
      icon: 'ri-webhook-line'
    }
  ], [t]);

  // Safe tab switching with validation
  const handleTabChange = useCallback((tabId: string) => {
    const validTabs: (keyof APIEndpoints)[] = ['payments', 'wallets', 'webhooks'];
    if (validTabs.includes(tabId as keyof APIEndpoints)) {
      setActiveTab(tabId as keyof APIEndpoints);
    } else {
      console.warn(`Invalid tab: ${tabId}`);
    }
  }, []);

  // Get method color with proper typing
  const getMethodColor = useCallback((method: APIEndpoint['method']): string => {
    switch (method) {
      case 'GET':
        return 'bg-green-100 text-green-800';
      case 'POST':
        return 'bg-blue-100 text-blue-800';
      case 'PUT':
        return 'bg-yellow-100 text-yellow-800';
      case 'DELETE':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }, []);

  // Safe endpoint access
  const currentEndpoints = apiEndpoints[activeTab] || [];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {t('developers.api.title') || 'API 参考文档'}
          </h2>
          <p className="text-xl text-gray-600">
            {t('developers.api.subtitle') || '完整的 REST API 文档和示例'}
          </p>
        </div>
        
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="border-b border-gray-200">
            <div className="flex" role="tablist" aria-label={t('developers.api.tabsLabel') || 'API Documentation Tabs'}>
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  role="tab"
                  aria-selected={activeTab === tab.id}
                  aria-controls={`panel-${tab.id}`}
                  className={`flex items-center px-6 py-4 text-sm font-medium transition-colors whitespace-nowrap cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset ${
                    activeTab === tab.id
                      ? 'border-b-2 border-blue-500 text-blue-600 bg-blue-50'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <i className={`${tab.icon} w-4 h-4 flex items-center justify-center mr-2`} aria-hidden="true"></i>
                  {tab.name}
                </button>
              ))}
            </div>
          </div>
          
          <div className="p-6">
            <div
              className="space-y-6"
              role="tabpanel"
              id={`panel-${activeTab}`}
              aria-labelledby={`tab-${activeTab}`}
            >
              {currentEndpoints.length > 0 ? (
                currentEndpoints.map((endpoint, index) => (
                  <div key={`${activeTab}-${index}`} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center mb-2">
                          <span className={`px-2 py-1 rounded text-xs font-medium mr-3 ${getMethodColor(endpoint.method)}`}>
                            {endpoint.method}
                          </span>
                          <code className="text-sm font-mono text-gray-800 bg-gray-100 px-2 py-1 rounded">
                            {endpoint.endpoint}
                          </code>
                        </div>
                        <p className="text-gray-600">{endpoint.description}</p>
                      </div>
                      <button
                        className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        aria-label={t('developers.api.viewDocs') || '查看文档'}
                        title={t('developers.api.viewDocs') || '查看文档'}
                      >
                        <i className="ri-external-link-line" aria-hidden="true"></i>
                      </button>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-2">
                        {t('developers.api.parameters') || '参数'}
                      </h4>
                      {endpoint.params.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                          {endpoint.params.map((param, idx) => (
                            <span
                              key={`${endpoint.endpoint}-${param}-${idx}`}
                              className="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm font-mono"
                            >
                              {param}
                            </span>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 text-sm italic">
                          {t('developers.api.noParameters') || '无参数'}
                        </p>
                      )}
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <i className="ri-file-list-line text-gray-400 text-4xl mb-4"></i>
                  <p className="text-gray-500">
                    {t('developers.api.noEndpoints') || '暂无API端点'}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}