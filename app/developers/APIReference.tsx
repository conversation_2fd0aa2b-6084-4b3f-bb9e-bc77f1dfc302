'use client';

import { useState } from 'react';

export default function APIReference() {
  const [activeTab, setActiveTab] = useState('payments');

  const apiEndpoints = {
    payments: [
      {
        method: 'POST',
        endpoint: '/v1/payments',
        description: '创建新的支付请求',
        params: ['amount', 'currency', 'crypto_currency', 'return_url']
      },
      {
        method: 'GET',
        endpoint: '/v1/payments/{id}',
        description: '获取支付详情',
        params: ['id']
      },
      {
        method: 'GET',
        endpoint: '/v1/payments',
        description: '获取支付列表',
        params: ['limit', 'offset', 'status']
      }
    ],
    wallets: [
      {
        method: 'GET',
        endpoint: '/v1/wallets',
        description: '获取钱包列表',
        params: ['currency']
      },
      {
        method: 'GET',
        endpoint: '/v1/wallets/{id}/balance',
        description: '获取钱包余额',
        params: ['id']
      },
      {
        method: 'POST',
        endpoint: '/v1/wallets/transfer',
        description: '钱包间转账',
        params: ['from_wallet', 'to_wallet', 'amount']
      }
    ],
    webhooks: [
      {
        method: 'POST',
        endpoint: '/v1/webhooks',
        description: '创建 Webhook',
        params: ['url', 'events']
      },
      {
        method: 'GET',
        endpoint: '/v1/webhooks',
        description: '获取 Webhook 列表',
        params: ['limit', 'offset']
      },
      {
        method: 'DELETE',
        endpoint: '/v1/webhooks/{id}',
        description: '删除 Webhook',
        params: ['id']
      }
    ]
  };

  const tabs = [
    { id: 'payments', name: '支付', icon: 'ri-money-dollar-circle-line' },
    { id: 'wallets', name: '钱包', icon: 'ri-wallet-line' },
    { id: 'webhooks', name: 'Webhooks', icon: 'ri-webhook-line' }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            API 参考文档
          </h2>
          <p className="text-xl text-gray-600">
            完整的 REST API 文档和示例
          </p>
        </div>
        
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="border-b border-gray-200">
            <div className="flex">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center px-6 py-4 text-sm font-medium transition-colors whitespace-nowrap cursor-pointer ${
                    activeTab === tab.id
                      ? 'border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-500 hover:text-gray-700'
                  }`}
                >
                  <i className={`${tab.icon} w-4 h-4 flex items-center justify-center mr-2`}></i>
                  {tab.name}
                </button>
              ))}
            </div>
          </div>
          
          <div className="p-6">
            <div className="space-y-6">
              {apiEndpoints[activeTab].map((endpoint, index) => (
                <div key={index} className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <div className="flex items-center mb-2">
                        <span className={`px-2 py-1 rounded text-xs font-medium mr-3 ${
                          endpoint.method === 'GET' ? 'bg-green-100 text-green-800' :
                          endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800' :
                          'bg-red-100 text-red-800'
                        }`}>
                          {endpoint.method}
                        </span>
                        <code className="text-sm font-mono text-gray-800">
                          {endpoint.endpoint}
                        </code>
                      </div>
                      <p className="text-gray-600">{endpoint.description}</p>
                    </div>
                    <button className="text-gray-400 hover:text-gray-600 transition-colors">
                      <i className="ri-external-link-line"></i>
                    </button>
                  </div>
                  
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-2">参数</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                      {endpoint.params.map((param, idx) => (
                        <span key={idx} className="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-sm">
                          {param}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}