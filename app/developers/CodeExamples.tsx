'use client';

import { useState, useMemo, useCallback } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

// TypeScript interfaces for better type safety
interface CodeExample {
  javascript: string;
  python: string;
  php: string;
  curl: string;
}

interface Language {
  id: keyof CodeExample;
  name: string;
  icon: string;
}

interface ExampleLink {
  name: string;
  href: string;
  icon: string;
}

export default function CodeExamples() {
  const { t } = useLanguage();
  const [activeLanguage, setActiveLanguage] = useState<keyof CodeExample>('javascript');
  const [copySuccess, setCopySuccess] = useState<string | null>(null);

  // Memoized code examples with internationalization
  const codeExamples = useMemo<CodeExample>(() => ({
    javascript: `// JavaScript SDK
import CryptoPay from '@cryptopay/node-sdk';

const cryptoPay = new CryptoPay('your_api_key');

// ${t('developers.codeExamples.createPayment') || '创建支付'}
const payment = await cryptoPay.payments.create({
  amount: 1000,
  currency: 'USD',
  cryptoCurrency: 'BTC',
  returnUrl: 'https://your-site.com/success',
  cancelUrl: 'https://your-site.com/cancel'
});

// ${t('developers.codeExamples.getPaymentStatus') || '获取支付状态'}
const status = await cryptoPay.payments.retrieve(payment.id);
console.log('Payment status:', status.status);`,

    python: `# Python SDK
from cryptopay import CryptoPay

crypto_pay = CryptoPay('your_api_key')

# ${t('developers.codeExamples.createPayment') || '创建支付'}
payment = crypto_pay.payments.create(
    amount=1000,
    currency='USD',
    crypto_currency='BTC',
    return_url='https://your-site.com/success',
    cancel_url='https://your-site.com/cancel'
)

# ${t('developers.codeExamples.getPaymentStatus') || '获取支付状态'}
status = crypto_pay.payments.retrieve(payment.id)
print(f'Payment status: {status.status}')`,

    php: `<?php
// PHP SDK
require_once 'vendor/autoload.php';

use CryptoPay\\CryptoPay;

$cryptoPay = new CryptoPay('your_api_key');

// ${t('developers.codeExamples.createPayment') || '创建支付'}
$payment = $cryptoPay->payments->create([
    'amount' => 1000,
    'currency' => 'USD',
    'crypto_currency' => 'BTC',
    'return_url' => 'https://your-site.com/success',
    'cancel_url' => 'https://your-site.com/cancel'
]);

// ${t('developers.codeExamples.getPaymentStatus') || '获取支付状态'}
$status = $cryptoPay->payments->retrieve($payment->id);
echo 'Payment status: ' . $status->status;`,

    curl: `# ${t('developers.codeExamples.curlExample') || 'cURL 示例'}
curl -X POST https://api.cryptopay.com/v1/payments \\
  -H "Authorization: Bearer your_api_key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "amount": 1000,
    "currency": "USD",
    "crypto_currency": "BTC",
    "return_url": "https://your-site.com/success",
    "cancel_url": "https://your-site.com/cancel"
  }'

# ${t('developers.codeExamples.getPaymentStatus') || '获取支付状态'}
curl -X GET https://api.cryptopay.com/v1/payments/payment_id \\
  -H "Authorization: Bearer your_api_key"`
  }), [t]);

  // Memoized languages with internationalization
  const languages = useMemo<Language[]>(() => [
    { id: 'javascript', name: 'JavaScript', icon: 'ri-javascript-line' },
    { id: 'python', name: 'Python', icon: 'ri-file-code-line' },
    { id: 'php', name: 'PHP', icon: 'ri-code-line' },
    { id: 'curl', name: 'cURL', icon: 'ri-terminal-line' }
  ], []);

  // Memoized example links
  const exampleLinks = useMemo<ExampleLink[]>(() => [
    {
      name: t('developers.codeExamples.examples.react') || 'React 示例',
      href: 'https://github.com/cryptopay/examples/tree/main/react',
      icon: 'ri-github-line'
    },
    {
      name: t('developers.codeExamples.examples.nodejs') || 'Node.js 示例',
      href: 'https://github.com/cryptopay/examples/tree/main/nodejs',
      icon: 'ri-github-line'
    },
    {
      name: t('developers.codeExamples.examples.flask') || 'Python Flask 示例',
      href: 'https://github.com/cryptopay/examples/tree/main/flask',
      icon: 'ri-github-line'
    }
  ], [t]);

  // Safe language switching with validation
  const handleLanguageChange = useCallback((langId: string) => {
    const validLanguages: (keyof CodeExample)[] = ['javascript', 'python', 'php', 'curl'];
    if (validLanguages.includes(langId as keyof CodeExample)) {
      setActiveLanguage(langId as keyof CodeExample);
    } else {
      console.warn(`Invalid language: ${langId}`);
    }
  }, []);

  // Copy to clipboard functionality
  const handleCopyCode = useCallback(async () => {
    try {
      const code = codeExamples[activeLanguage];
      await navigator.clipboard.writeText(code);
      setCopySuccess(activeLanguage);

      // Clear success message after 2 seconds
      setTimeout(() => {
        setCopySuccess(null);
      }, 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = codeExamples[activeLanguage];
      document.body.appendChild(textArea);
      textArea.select();
      try {
        document.execCommand('copy');
        setCopySuccess(activeLanguage);
        setTimeout(() => setCopySuccess(null), 2000);
      } catch (fallbackErr) {
        console.error('Fallback copy failed:', fallbackErr);
      }
      document.body.removeChild(textArea);
    }
  }, [codeExamples, activeLanguage]);

  // Get current language name safely
  const currentLanguageName = useMemo(() => {
    const lang = languages.find(l => l.id === activeLanguage);
    return lang?.name || 'Unknown';
  }, [languages, activeLanguage]);

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            {t('developers.codeExamples.title') || '代码示例'}
          </h2>
          <p className="text-xl text-gray-600">
            {t('developers.codeExamples.subtitle') || '支持多种编程语言的完整示例代码'}
          </p>
        </div>
        
        <div className="bg-gray-900 rounded-xl overflow-hidden">
          <div className="border-b border-gray-700">
            <div
              className="flex"
              role="tablist"
              aria-label={t('developers.codeExamples.languageTabsLabel') || '编程语言选择'}
            >
              {languages.map((lang) => (
                <button
                  key={lang.id}
                  onClick={() => handleLanguageChange(lang.id)}
                  role="tab"
                  aria-selected={activeLanguage === lang.id}
                  aria-controls={`code-panel-${lang.id}`}
                  className={`flex items-center px-6 py-4 text-sm font-medium transition-colors whitespace-nowrap cursor-pointer focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-inset ${
                    activeLanguage === lang.id
                      ? 'bg-gray-800 text-white border-b-2 border-blue-500'
                      : 'text-gray-400 hover:text-white hover:bg-gray-800'
                  }`}
                >
                  <i className={`${lang.icon} w-4 h-4 flex items-center justify-center mr-2`} aria-hidden="true"></i>
                  {lang.name}
                </button>
              ))}
            </div>
          </div>
          
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white font-semibold">
                {currentLanguageName} {t('developers.codeExamples.example') || '示例'}
              </h3>
              <button
                onClick={handleCopyCode}
                className="flex items-center text-gray-400 hover:text-white transition-colors p-2 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                aria-label={t('developers.codeExamples.copyCode') || '复制代码'}
                title={t('developers.codeExamples.copyCode') || '复制代码'}
              >
                {copySuccess === activeLanguage ? (
                  <>
                    <i className="ri-check-line mr-1" aria-hidden="true"></i>
                    <span className="text-sm">{t('developers.codeExamples.copied') || '已复制'}</span>
                  </>
                ) : (
                  <i className="ri-file-copy-line" aria-hidden="true"></i>
                )}
              </button>
            </div>
            <div
              role="tabpanel"
              id={`code-panel-${activeLanguage}`}
              aria-labelledby={`tab-${activeLanguage}`}
            >
              <pre className="text-green-400 text-sm overflow-x-auto bg-gray-800 p-4 rounded-lg">
                <code>{codeExamples[activeLanguage]}</code>
              </pre>
            </div>
          </div>
        </div>
        
        <div className="mt-12 bg-blue-50 rounded-xl p-6">
          <div className="flex items-start">
            <i className="ri-lightbulb-line text-blue-600 text-xl mr-3 mt-1" aria-hidden="true"></i>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {t('developers.codeExamples.moreExamples.title') || '更多示例'}
              </h3>
              <p className="text-gray-600 mb-4">
                {t('developers.codeExamples.moreExamples.description') || '查看我们的 GitHub 仓库获取更多完整的示例项目和集成指南'}
              </p>
              <div className="flex flex-wrap gap-3">
                {exampleLinks.map((link, index) => (
                  <a
                    key={index}
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded px-2 py-1"
                  >
                    <i className={`${link.icon} w-4 h-4 flex items-center justify-center mr-1`} aria-hidden="true"></i>
                    {link.name}
                    <i className="ri-external-link-line w-3 h-3 ml-1" aria-hidden="true"></i>
                  </a>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}