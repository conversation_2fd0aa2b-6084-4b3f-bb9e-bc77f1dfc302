'use client';

import { useState } from 'react';

export default function CodeExamples() {
  const [activeLanguage, setActiveLanguage] = useState('javascript');

  const codeExamples = {
    javascript: `// JavaScript SDK
import CryptoPay from '@cryptopay/node-sdk';

const cryptoPay = new CryptoPay('your_api_key');

// 创建支付
const payment = await cryptoPay.payments.create({
  amount: 1000,
  currency: 'USD',
  cryptoCurrency: 'BTC',
  returnUrl: 'https://your-site.com/success',
  cancelUrl: 'https://your-site.com/cancel'
});

// 获取支付状态
const status = await cryptoPay.payments.retrieve(payment.id);
console.log('Payment status:', status.status);`,

    python: `# Python SDK
from cryptopay import CryptoPay

crypto_pay = CryptoPay('your_api_key')

# 创建支付
payment = crypto_pay.payments.create(
    amount=1000,
    currency='USD',
    crypto_currency='BTC',
    return_url='https://your-site.com/success',
    cancel_url='https://your-site.com/cancel'
)

# 获取支付状态
status = crypto_pay.payments.retrieve(payment.id)
print(f'Payment status: {status.status}')`,

    php: `<?php
// PHP SDK
require_once 'vendor/autoload.php';

use CryptoPay\\CryptoPay;

$cryptoPay = new CryptoPay('your_api_key');

// 创建支付
$payment = $cryptoPay->payments->create([
    'amount' => 1000,
    'currency' => 'USD',
    'crypto_currency' => 'BTC',
    'return_url' => 'https://your-site.com/success',
    'cancel_url' => 'https://your-site.com/cancel'
]);

// 获取支付状态
$status = $cryptoPay->payments->retrieve($payment->id);
echo 'Payment status: ' . $status->status;`,

    curl: `# cURL 示例
curl -X POST https://api.cryptopay.com/v1/payments \\
  -H "Authorization: Bearer your_api_key" \\
  -H "Content-Type: application/json" \\
  -d '{
    "amount": 1000,
    "currency": "USD",
    "crypto_currency": "BTC",
    "return_url": "https://your-site.com/success",
    "cancel_url": "https://your-site.com/cancel"
  }'

# 获取支付状态
curl -X GET https://api.cryptopay.com/v1/payments/payment_id \\
  -H "Authorization: Bearer your_api_key"`
  };

  const languages = [
    { id: 'javascript', name: 'JavaScript', icon: 'ri-javascript-line' },
    { id: 'python', name: 'Python', icon: 'ri-file-code-line' },
    { id: 'php', name: 'PHP', icon: 'ri-code-line' },
    { id: 'curl', name: 'cURL', icon: 'ri-terminal-line' }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            代码示例
          </h2>
          <p className="text-xl text-gray-600">
            支持多种编程语言的完整示例代码
          </p>
        </div>
        
        <div className="bg-gray-900 rounded-xl overflow-hidden">
          <div className="border-b border-gray-700">
            <div className="flex">
              {languages.map((lang) => (
                <button
                  key={lang.id}
                  onClick={() => setActiveLanguage(lang.id)}
                  className={`flex items-center px-6 py-4 text-sm font-medium transition-colors whitespace-nowrap cursor-pointer ${
                    activeLanguage === lang.id
                      ? 'bg-gray-800 text-white'
                      : 'text-gray-400 hover:text-white'
                  }`}
                >
                  <i className={`${lang.icon} w-4 h-4 flex items-center justify-center mr-2`}></i>
                  {lang.name}
                </button>
              ))}
            </div>
          </div>
          
          <div className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-white font-semibold">
                {languages.find(l => l.id === activeLanguage)?.name} 示例
              </h3>
              <button className="text-gray-400 hover:text-white transition-colors">
                <i className="ri-file-copy-line"></i>
              </button>
            </div>
            <pre className="text-green-400 text-sm overflow-x-auto">
              <code>{codeExamples[activeLanguage]}</code>
            </pre>
          </div>
        </div>
        
        <div className="mt-12 bg-blue-50 rounded-xl p-6">
          <div className="flex items-start">
            <i className="ri-lightbulb-line text-blue-600 text-xl mr-3 mt-1"></i>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">更多示例</h3>
              <p className="text-gray-600 mb-4">
                查看我们的 GitHub 仓库获取更多完整的示例项目和集成指南
              </p>
              <div className="flex flex-wrap gap-3">
                <a href="#" className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium">
                  <i className="ri-github-line w-4 h-4 flex items-center justify-center mr-1"></i>
                  React 示例
                </a>
                <a href="#" className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium">
                  <i className="ri-github-line w-4 h-4 flex items-center justify-center mr-1"></i>
                  Node.js 示例
                </a>
                <a href="#" className="inline-flex items-center text-blue-600 hover:text-blue-700 text-sm font-medium">
                  <i className="ri-github-line w-4 h-4 flex items-center justify-center mr-1"></i>
                  Python Flask 示例
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}