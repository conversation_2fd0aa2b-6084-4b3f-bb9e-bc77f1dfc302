'use client';

export default function SDKSection() {
  const sdks = [
    {
      name: 'Node.js',
      icon: 'ri-nodejs-line',
      description: '用于服务器端 JavaScript 应用',
      install: 'npm install @cryptopay/node-sdk',
      version: 'v2.1.0',
      downloads: '25,000+',
      color: 'bg-green-600'
    },
    {
      name: 'Python',
      icon: 'ri-file-code-line',
      description: '用于 Python Web 应用和脚本',
      install: 'pip install cryptopay-python',
      version: 'v1.8.3',
      downloads: '18,000+',
      color: 'bg-blue-600'
    },
    {
      name: 'PHP',
      icon: 'ri-code-line',
      description: '用于 PHP Web 应用',
      install: 'composer require cryptopay/php-sdk',
      version: 'v1.5.2',
      downloads: '12,000+',
      color: 'bg-purple-600'
    },
    {
      name: 'Java',
      icon: 'ri-cup-line',
      description: '用于 Java 和 Spring Boot 应用',
      install: 'maven: cryptopay-java-sdk',
      version: 'v1.3.1',
      downloads: '8,000+',
      color: 'bg-orange-600'
    },
    {
      name: '<PERSON>',
      icon: 'ri-gem-line',
      description: '用于 Ruby 和 Rails 应用',
      install: 'gem install cryptopay-ruby',
      version: 'v1.2.0',
      downloads: '5,000+',
      color: 'bg-red-600'
    },
    {
      name: 'Go',
      icon: 'ri-code-s-slash-line',
      description: '用于 Go 语言应用',
      install: 'go get github.com/cryptopay/go-sdk',
      version: 'v1.1.0',
      downloads: '3,000+',
      color: 'bg-cyan-600'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            官方 SDK
          </h2>
          <p className="text-xl text-gray-600">
            支持主流编程语言的官方 SDK，让集成更简单
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {sdks.map((sdk, index) => (
            <div key={index} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
              <div className="flex items-center mb-4">
                <div className={`w-12 h-12 ${sdk.color} rounded-lg flex items-center justify-center mr-4`}>
                  <i className={`${sdk.icon} text-white text-2xl`}></i>
                </div>
                <div>
                  <h3 className="text-xl font-semibold text-gray-900">{sdk.name}</h3>
                  <span className="text-sm text-gray-500">{sdk.version}</span>
                </div>
              </div>
              
              <p className="text-gray-600 mb-4">{sdk.description}</p>
              
              <div className="bg-gray-100 rounded-lg p-3 mb-4">
                <code className="text-sm text-gray-800">{sdk.install}</code>
              </div>
              
              <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                <span>{sdk.downloads} 下载</span>
                <span>MIT 许可</span>
              </div>
              
              <div className="flex gap-3">
                <button className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 text-sm font-medium whitespace-nowrap cursor-pointer">
                  <i className="ri-download-line w-4 h-4 flex items-center justify-center mr-1"></i>
                  下载
                </button>
                <button className="flex-1 border border-gray-300 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-50 transition-colors duration-200 text-sm font-medium whitespace-nowrap cursor-pointer">
                  <i className="ri-book-line w-4 h-4 flex items-center justify-center mr-1"></i>
                  文档
                </button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-12 text-center">
          <div className="bg-white rounded-xl shadow-md p-8">
            <i className="ri-github-line text-4xl text-gray-400 mb-4"></i>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">
              找不到您需要的语言？
            </h3>
            <p className="text-gray-600 mb-6">
              我们的 REST API 支持任何能发送 HTTP 请求的编程语言。查看我们的 GitHub 仓库获取更多示例。
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gray-900 text-white py-3 px-6 rounded-lg hover:bg-gray-800 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                <i className="ri-github-line w-5 h-5 flex items-center justify-center mr-2"></i>
                查看 GitHub
              </button>
              <button className="border border-gray-300 text-gray-700 py-3 px-6 rounded-lg hover:bg-gray-50 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                <i className="ri-mail-line w-5 h-5 flex items-center justify-center mr-2"></i>
                联系我们
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}