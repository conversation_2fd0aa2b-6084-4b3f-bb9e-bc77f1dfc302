'use client';

import { useLanguage } from '../../contexts/LanguageContext';
import Header from '../../components/Header';
import Footer from '../../components/Footer';

export default function I18nDemo() {
  const { t, language, setLanguage } = useLanguage();

  return (
    <div className="min-h-screen bg-white">
      <Header />
      
      <main className="pt-24 pb-16">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              国际化演示 / Internationalization Demo
            </h1>
            <p className="text-lg text-gray-600">
              当前语言 / Current Language: <span className="font-semibold">{language === 'zh' ? '中文' : 'English'}</span>
            </p>
          </div>

          <div className="bg-white rounded-2xl shadow-xl border border-gray-200 p-8 mb-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">翻译示例 / Translation Examples</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">导航 / Navigation</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>首页:</strong> {t('navigation.home')}</div>
                  <div><strong>解决方案:</strong> {t('navigation.solutions')}</div>
                  <div><strong>定价:</strong> {t('navigation.pricing')}</div>
                  <div><strong>开发者:</strong> {t('navigation.developers')}</div>
                  <div><strong>支持:</strong> {t('navigation.support')}</div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">常用词汇 / Common Terms</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>加载中:</strong> {t('common.loading')}</div>
                  <div><strong>立即开始:</strong> {t('common.getStarted')}</div>
                  <div><strong>了解更多:</strong> {t('common.learnMore')}</div>
                  <div><strong>查看文档:</strong> {t('common.viewDocumentation')}</div>
                  <div><strong>联系我们:</strong> {t('common.contactUs')}</div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">英雄区域 / Hero Section</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>标语:</strong> {t('hero.tagline')}</div>
                  <div><strong>标题:</strong> {t('hero.title')}</div>
                  <div><strong>副标题:</strong> {t('hero.subtitle')}</div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-800">统计数据 / Statistics</h3>
                <div className="space-y-2 text-sm">
                  <div><strong>活跃用户:</strong> {t('hero.stats.activeUsers')}</div>
                  <div><strong>交易笔数:</strong> {t('hero.stats.transactions')}</div>
                  <div><strong>覆盖国家:</strong> {t('hero.stats.countries')}</div>
                  <div><strong>交易量:</strong> {t('hero.stats.volume')}</div>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-gray-900 mb-4">语言切换测试 / Language Switch Test</h2>
            <p className="text-gray-600 mb-6">
              点击下面的按钮切换语言，观察页面内容的变化。
              <br />
              Click the buttons below to switch languages and observe the content changes.
            </p>
            
            <div className="flex gap-4">
              <button
                onClick={() => setLanguage('zh')}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                  language === 'zh'
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                🇨🇳 中文
              </button>
              <button
                onClick={() => setLanguage('en')}
                className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 ${
                  language === 'en'
                    ? 'bg-blue-600 text-white shadow-lg'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                🇺🇸 English
              </button>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
