
'use client';

import { useState } from 'react';

export default function TransactionHistory() {
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterType, setFilterType] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const transactions = [
    {
      id: 'TX001234',
      type: 'buy',
      amount: '$2,500.00',
      crypto: 'BTC',
      cryptoAmount: '0.08245',
      status: 'completed',
      fee: '$12.50',
      time: '2024-01-15 14:32:18',
      method: 'Bank Transfer',
      hash: '0x1234...5678'
    },
    {
      id: 'TX001235',
      type: 'sell',
      amount: '$1,200.00',
      crypto: 'ETH',
      cryptoAmount: '0.52143',
      status: 'completed',
      fee: '$6.00',
      time: '2024-01-15 13:45:22',
      method: 'Credit Card',
      hash: '0x2345...6789'
    },
    {
      id: 'TX001236',
      type: 'buy',
      amount: '$800.00',
      crypto: 'USDT',
      cryptoAmount: '800.00',
      status: 'pending',
      fee: '$4.00',
      time: '2024-01-15 12:18:45',
      method: 'Bank Transfer',
      hash: '0x3456...7890'
    },
    {
      id: 'TX001237',
      type: 'sell',
      amount: '$3,200.00',
      crypto: 'BTC',
      cryptoAmount: '0.10523',
      status: 'completed',
      fee: '$16.00',
      time: '2024-01-15 11:22:10',
      method: 'Bank Transfer',
      hash: '0x4567...8901'
    },
    {
      id: 'TX001238',
      type: 'buy',
      amount: '$650.00',
      crypto: 'ADA',
      cryptoAmount: '1,250.00',
      status: 'failed',
      fee: '$0.00',
      time: '2024-01-15 10:15:33',
      method: 'Credit Card',
      hash: '0x5678...9012'
    },
    {
      id: 'TX001239',
      type: 'sell',
      amount: '$1,800.00',
      crypto: 'ETH',
      cryptoAmount: '0.78234',
      status: 'completed',
      fee: '$9.00',
      time: '2024-01-15 09:45:18',
      method: 'Bank Transfer',
      hash: '0x6789...0123'
    }
  ];

  const filteredTransactions = transactions.filter(tx => {
    const matchesStatus = filterStatus === 'all' || tx.status === filterStatus;
    const matchesType = filterType === 'all' || tx.type === filterType;
    const matchesSearch = tx.id.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         tx.crypto.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesType && matchesSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'completed':
        return '已完成';
      case 'pending':
        return '处理中';
      case 'failed':
        return '失败';
      default:
        return '未知';
    }
  };

  const getTypeColor = (type: string) => {
    return type === 'buy' ? 'text-green-600' : 'text-red-600';
  };

  const getTypeText = (type: string) => {
    return type === 'buy' ? '买入' : '卖出';
  };

  return (
    <div className="space-y-6">
      {/* 筛选和搜索 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-xl">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="搜索交易ID或加密货币..."
                className="w-full px-4 py-3 pl-12 pr-4 text-gray-700 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              <i className="ri-search-line absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
          </div>
          
          <div className="flex gap-4">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-4 py-3 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 pr-8"
            >
              <option value="all">所有状态</option>
              <option value="completed">已完成</option>
              <option value="pending">处理中</option>
              <option value="failed">失败</option>
            </select>
            
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-4 py-3 bg-white border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 pr-8"
            >
              <option value="all">所有类型</option>
              <option value="buy">买入</option>
              <option value="sell">卖出</option>
            </select>
          </div>
        </div>
      </div>

      {/* 交易列表 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-100 shadow-xl overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gradient-to-r from-gray-50 to-blue-50 border-b border-gray-200">
                <th className="text-left py-4 px-6 text-sm font-semibold text-gray-700">交易ID</th>
                <th className="text-left py-4 px-6 text-sm font-semibold text-gray-700">类型</th>
                <th className="text-left py-4 px-6 text-sm font-semibold text-gray-700">金额</th>
                <th className="text-left py-4 px-6 text-sm font-semibold text-gray-700">加密货币</th>
                <th className="text-left py-4 px-6 text-sm font-semibold text-gray-700">状态</th>
                <th className="text-left py-4 px-6 text-sm font-semibold text-gray-700">手续费</th>
                <th className="text-left py-4 px-6 text-sm font-semibold text-gray-700">时间</th>
                <th className="text-left py-4 px-6 text-sm font-semibold text-gray-700">操作</th>
              </tr>
            </thead>
            <tbody>
              {filteredTransactions.map((tx, index) => (
                <tr key={index} className="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200">
                  <td className="py-4 px-6">
                    <div className="font-medium text-gray-900">{tx.id}</div>
                    <div className="text-sm text-gray-500">{tx.method}</div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                        tx.type === 'buy' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                      }`}>
                        <i className={`${tx.type === 'buy' ? 'ri-arrow-down-line' : 'ri-arrow-up-line'} text-sm`}></i>
                      </div>
                      <span className={`font-medium ${getTypeColor(tx.type)}`}>
                        {getTypeText(tx.type)}
                      </span>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="font-medium text-gray-900">{tx.amount}</div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex items-center">
                      <img 
                        src={`https://cryptologos.cc/logos/${tx.crypto.toLowerCase()}-${tx.crypto.toLowerCase()}-logo.png`}
                        alt={tx.crypto}
                        className="w-6 h-6 rounded-full mr-2"
                        onError={(e) => {
                          e.currentTarget.src = `https://readdy.ai/api/search-image?query=$%7Btx.crypto%7D%20cryptocurrency%20logo%20icon&width=24&height=24&seq=${tx.crypto}&orientation=squarish`;
                        }}
                      />
                      <div>
                        <div className="font-medium text-gray-900">{tx.crypto}</div>
                        <div className="text-sm text-gray-500">{tx.cryptoAmount}</div>
                      </div>
                    </div>
                  </td>
                  <td className="py-4 px-6">
                    <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(tx.status)}`}>
                      {getStatusText(tx.status)}
                    </span>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-gray-900">{tx.fee}</div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="text-sm text-gray-500">{tx.time}</div>
                  </td>
                  <td className="py-4 px-6">
                    <div className="flex space-x-2">
                      <button className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer">
                        <i className="ri-eye-line w-4 h-4 flex items-center justify-center"></i>
                      </button>
                      <button className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors duration-200 cursor-pointer">
                        <i className="ri-download-line w-4 h-4 flex items-center justify-center"></i>
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 分页 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-xl">
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-600">
            显示 1-6 项，共 {filteredTransactions.length} 项
          </div>
          <div className="flex items-center space-x-2">
            <button className="px-4 py-2 text-gray-600 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
              上一页
            </button>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 cursor-pointer">
              1
            </button>
            <button className="px-4 py-2 text-gray-600 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
              2
            </button>
            <button className="px-4 py-2 text-gray-600 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
              3
            </button>
            <button className="px-4 py-2 text-gray-600 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200 cursor-pointer">
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
