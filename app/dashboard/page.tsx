'use client';

import React, { Suspense, useEffect, useState, useCallback, Component, memo } from 'react';
import dynamic from 'next/dynamic';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import DashboardLayout from './DashboardLayout';
import { useLanguage } from '@/contexts/LanguageContext';

// Component-specific loading component (defined early for use in dynamic imports)
function ComponentLoading({ name }: { name: string }) {
  const { t } = useLanguage();

  // Sanitize the name for translation key
  const sanitizedName = name.replace(/\s+/g, '');

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-100 shadow-xl">
      <div className="flex items-center justify-center space-x-3">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <span className="text-gray-600">
          {t(`dashboard.loading${sanitizedName}`) || `Loading ${name}...`}
        </span>
      </div>
    </div>
  );
}

// Lazy load dashboard components for better performance
const DashboardOverview = dynamic(() => import('./DashboardOverview'), {
  loading: () => <ComponentLoading name="Overview" />
});

const TransactionHistory = dynamic(() => import('./TransactionHistory'), {
  loading: () => <ComponentLoading name="Transactions" />
});

const WalletManagement = dynamic(() => import('./WalletManagement'), {
  loading: () => <ComponentLoading name="Wallet" />
});

const APIKeyManager = dynamic(() => import('./APIKeyManager'), {
  loading: () => <ComponentLoading name="API Keys" />
});

const ProfileSettings = dynamic(() => import('./ProfileSettings'), {
  loading: () => <ComponentLoading name="Settings" />
});

// TypeScript interfaces
interface DashboardErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

interface DashboardErrorBoundaryProps {
  children: React.ReactNode;
}

// Error Boundary Class Component
class DashboardErrorBoundary extends Component<DashboardErrorBoundaryProps, DashboardErrorBoundaryState> {
  constructor(props: DashboardErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): DashboardErrorBoundaryState {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <ErrorFallback error={this.state.error} />;
    }

    return this.props.children;
  }
}

// Error Fallback Component
function ErrorFallback({ error }: { error?: Error }) {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-white to-orange-50 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-red-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-orange-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="text-center relative z-10 max-w-md mx-auto px-4">
        <div className="w-20 h-20 bg-gradient-to-r from-red-500 to-orange-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
          <i className="ri-error-warning-line text-white text-3xl"></i>
        </div>
        <h2 className="text-2xl font-bold text-gray-900 mb-4">
          {t('dashboard.error.title') || 'Something went wrong'}
        </h2>
        <p className="text-gray-600 mb-6">
          {t('dashboard.error.description') || 'We encountered an error while loading the dashboard. Please try refreshing the page.'}
        </p>
        {process.env.NODE_ENV === 'development' && error && (
          <details className="mb-4 text-left bg-gray-100 p-4 rounded-lg">
            <summary className="cursor-pointer font-medium text-gray-700 mb-2">Error Details</summary>
            <pre className="text-xs text-red-600 overflow-auto">{error.stack}</pre>
          </details>
        )}
        <button
          onClick={() => window.location.reload()}
          className="bg-gradient-to-r from-red-500 to-orange-600 text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-200"
        >
          {t('dashboard.error.refresh') || 'Refresh Page'}
        </button>
      </div>
    </div>
  );
}

// Loading Component
function DashboardLoading() {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="text-center relative z-10">
        <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-2xl">
          <i className="ri-dashboard-line text-white text-3xl"></i>
        </div>
        <div className="text-gray-600 font-medium text-2xl mb-4">
          {t('dashboard.loading') || 'Loading Dashboard...'}
        </div>
        <div className="flex justify-center mb-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
        <div className="text-sm text-gray-500">
          {t('dashboard.loadingDescription') || 'Preparing your financial overview'}
        </div>
      </div>
    </div>
  );
}

// Memoized Dashboard component for better performance
const Dashboard = memo(function Dashboard() {
  const { t } = useLanguage();
  const [activeSection, setActiveSection] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize dashboard with proper error handling
  useEffect(() => {
    let isMounted = true;

    const initializeDashboard = async () => {
      try {
        // Simulate initial loading and potential API calls
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (isMounted) {
          setIsLoading(false);
        }
      } catch (err) {
        console.error('Dashboard initialization failed:', err);
        if (isMounted) {
          setError(err instanceof Error ? err.message : 'Failed to initialize dashboard');
          setIsLoading(false);
        }
      }
    };

    initializeDashboard();

    // Cleanup function
    return () => {
      isMounted = false;
    };
  }, []);

  // Handle section navigation with validation
  const handleSectionChange = useCallback((section: string) => {
    const validSections = ['overview', 'transactions', 'wallet', 'api', 'settings'];
    if (validSections.includes(section)) {
      setActiveSection(section);
    } else {
      console.warn(`Invalid section: ${section}`);
    }
  }, []);

  // Error state
  if (error) {
    return <ErrorFallback error={new Error(error)} />;
  }

  // Loading state
  if (isLoading) {
    return <DashboardLoading />;
  }

  return (
    <DashboardErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
        <Header />
        <main>
          <Suspense fallback={<DashboardLoading />}>
            <DashboardLayout
              activeTab={activeSection}
              setActiveTab={handleSectionChange}
            >
              <div className="space-y-10">
                {activeSection === 'overview' && (
                  <LazyComponentWrapper
                    fallback={<ComponentLoading name="Overview" />}
                    name="Overview"
                  >
                    <DashboardOverview />
                  </LazyComponentWrapper>
                )}

                {activeSection === 'transactions' && (
                  <LazyComponentWrapper
                    fallback={<ComponentLoading name="Transactions" />}
                    name="Transactions"
                  >
                    <TransactionHistory />
                  </LazyComponentWrapper>
                )}

                {activeSection === 'wallet' && (
                  <LazyComponentWrapper
                    fallback={<ComponentLoading name="Wallet" />}
                    name="Wallet"
                  >
                    <WalletManagement />
                  </LazyComponentWrapper>
                )}

                {activeSection === 'api' && (
                  <LazyComponentWrapper
                    fallback={<ComponentLoading name="API Keys" />}
                    name="API Keys"
                  >
                    <APIKeyManager />
                  </LazyComponentWrapper>
                )}

                {activeSection === 'settings' && (
                  <LazyComponentWrapper
                    fallback={<ComponentLoading name="Settings" />}
                    name="Settings"
                  >
                    <ProfileSettings />
                  </LazyComponentWrapper>
                )}
              </div>
            </DashboardLayout>
          </Suspense>
        </main>
        <Footer />
      </div>
    </DashboardErrorBoundary>
  );
});

export default Dashboard;

// Lazy loading wrapper with error boundary
function LazyComponentWrapper({
  children,
  fallback,
  name
}: {
  children: React.ReactNode;
  fallback: React.ReactNode;
  name: string;
}) {
  return (
    <DashboardErrorBoundary>
      <Suspense fallback={fallback}>
        {children}
      </Suspense>
    </DashboardErrorBoundary>
  );
}