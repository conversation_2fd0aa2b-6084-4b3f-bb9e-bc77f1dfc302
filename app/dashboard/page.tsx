'use client';

import React, { Suspense, ErrorBoundary } from 'react';
import { useState, useCallback } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import DashboardLayout from './DashboardLayout';
import DashboardOverview from './DashboardOverview';
import TransactionHistory from './TransactionHistory';
import WalletManagement from './WalletManagement';
import APIKeyManager from './APIKeyManager';
import ProfileSettings from './ProfileSettings';
import { useLanguage } from '@/contexts/LanguageContext';

// Error Boundary Component
function DashboardErrorBoundary({ children }: { children: React.ReactNode }) {
  const { t } = useLanguage();

  return (
    <ErrorBoundary
      fallback={
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-white to-orange-50 relative overflow-hidden">
          <div className="absolute inset-0">
            <div className="absolute top-20 left-20 w-96 h-96 bg-red-400/20 rounded-full blur-3xl animate-pulse"></div>
            <div className="absolute bottom-20 right-20 w-80 h-80 bg-orange-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
          </div>

          <div className="text-center relative z-10 max-w-md mx-auto px-4">
            <div className="w-20 h-20 bg-gradient-to-r from-red-500 to-orange-600 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-2xl">
              <i className="ri-error-warning-line text-white text-3xl"></i>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-4">
              {t('dashboard.error.title') || 'Something went wrong'}
            </h2>
            <p className="text-gray-600 mb-6">
              {t('dashboard.error.description') || 'We encountered an error while loading the dashboard. Please try refreshing the page.'}
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-gradient-to-r from-red-500 to-orange-600 text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-200"
            >
              {t('dashboard.error.refresh') || 'Refresh Page'}
            </button>
          </div>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  );
}

// Loading Component
function DashboardLoading() {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      <div className="text-center relative z-10">
        <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-2xl">
          <i className="ri-dashboard-line text-white text-3xl"></i>
        </div>
        <div className="text-gray-600 font-medium text-2xl mb-4">
          {t('dashboard.loading') || 'Loading Dashboard...'}
        </div>
        <div className="flex justify-center mb-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>
        <div className="text-sm text-gray-500">
          {t('dashboard.loadingDescription') || 'Preparing your financial overview'}
        </div>
      </div>
    </div>
  );
}

export default function Dashboard() {
  const { t } = useLanguage();
  const [activeSection, setActiveSection] = useState('overview');
  const [isLoading, setIsLoading] = useState(true);

  // Simulate initial loading
  const handleInitialLoad = useCallback(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  // Initialize dashboard
  React.useEffect(() => {
    const cleanup = handleInitialLoad();
    return cleanup;
  }, [handleInitialLoad]);

  // Handle section navigation
  const handleSectionChange = useCallback((section: string) => {
    setActiveSection(section);
  }, []);

  if (isLoading) {
    return <DashboardLoading />;
  }

  return (
    <DashboardErrorBoundary>
      <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50">
        <Header />
        <main>
          <Suspense fallback={<DashboardLoading />}>
            <DashboardLayout
              activeTab={activeSection}
              setActiveTab={handleSectionChange}
            >
              <div className="space-y-10">
                {activeSection === 'overview' && (
                  <Suspense fallback={<ComponentLoading name="Overview" />}>
                    <DashboardOverview />
                  </Suspense>
                )}

                {activeSection === 'transactions' && (
                  <Suspense fallback={<ComponentLoading name="Transactions" />}>
                    <TransactionHistory />
                  </Suspense>
                )}

                {activeSection === 'wallet' && (
                  <Suspense fallback={<ComponentLoading name="Wallet" />}>
                    <WalletManagement />
                  </Suspense>
                )}

                {activeSection === 'api' && (
                  <Suspense fallback={<ComponentLoading name="API Keys" />}>
                    <APIKeyManager />
                  </Suspense>
                )}

                {activeSection === 'settings' && (
                  <Suspense fallback={<ComponentLoading name="Settings" />}>
                    <ProfileSettings />
                  </Suspense>
                )}
              </div>
            </DashboardLayout>
          </Suspense>
        </main>
        <Footer />
      </div>
    </DashboardErrorBoundary>
  );
}

// Component-specific loading component
function ComponentLoading({ name }: { name: string }) {
  const { t } = useLanguage();

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-100 shadow-xl">
      <div className="flex items-center justify-center space-x-3">
        <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
        <span className="text-gray-600">
          {t(`dashboard.loading${name}`) || `Loading ${name}...`}
        </span>
      </div>
    </div>
  );
}