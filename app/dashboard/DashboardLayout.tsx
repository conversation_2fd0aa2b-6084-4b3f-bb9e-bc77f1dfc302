
'use client';

import { ReactNode } from 'react';
import Link from 'next/link';
import { useLanguage } from '@/contexts/LanguageContext';

interface DashboardLayoutProps {
  children: ReactNode;
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

export default function DashboardLayout({ children, activeTab, setActiveTab }: DashboardLayoutProps) {
  const { t } = useLanguage();

  const menuItems = [
    { id: 'overview', label: t('dashboard.menu.overview'), icon: 'ri-dashboard-line' },
    { id: 'transactions', label: t('dashboard.menu.transactions'), icon: 'ri-exchange-line' },
    { id: 'wallet', label: t('dashboard.menu.wallet'), icon: 'ri-wallet-3-line' },
    { id: 'api', label: t('dashboard.menu.api'), icon: 'ri-code-s-slash-line' },
    { id: 'settings', label: t('dashboard.menu.settings'), icon: 'ri-settings-3-line' }
  ];

  const quickActions = [
    { label: t('dashboard.quickActions.deposit'), icon: 'ri-add-circle-line', href: '/buy-crypto' },
    { label: t('dashboard.quickActions.transfer'), icon: 'ri-send-plane-line', href: '/solutions' },
    { label: t('dashboard.quickActions.receive'), icon: 'ri-qr-code-line', href: '/pricing' },
    { label: t('dashboard.quickActions.exchange'), icon: 'ri-exchange-2-line', href: '/developers' }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      <div className="flex">
        {/* 侧边栏 */}
        <div className="w-72 bg-white shadow-xl border-r border-gray-100">
          {/* 品牌区域 */}
          <div className="p-6 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center mr-3">
                <i className="ri-wallet-3-line text-white text-lg"></i>
              </div>
              <div>
                <div className="font-bold text-gray-900 text-lg">CryptoPay</div>
                <div className="text-sm text-gray-500">{t('dashboard.brand.enterprise')}</div>
              </div>
            </div>
          </div>

          {/* 企业信息 */}
          <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-12 h-12 bg-gradient-to-r from-orange-400 to-pink-400 rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                科
              </div>
              <div className="flex-1">
                <div className="font-semibold text-gray-900 text-sm">科技创新有限公司</div>
                <div className="text-xs text-gray-500">ID: ENT-2024-001</div>
                <div className="flex items-center mt-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                  <span className="text-xs text-green-600">已认证</span>
                </div>
              </div>
            </div>
          </div>

          {/* 导航菜单 */}
          <nav className="p-4 space-y-2">
            {menuItems.map((item) => (
              <button
                key={item.id}
                onClick={() => setActiveTab(item.id)}
                className={`w-full flex items-center px-4 py-3 rounded-xl text-left transition-all duration-200 ${
                  activeTab === item.id 
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg' 
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <i className={`${item.icon} text-lg mr-3`}></i>
                <span className="font-medium">{item.label}</span>
              </button>
            ))}
          </nav>

          {/* 快捷操作 */}
          <div className="p-4 border-t border-gray-100">
            <div className="text-sm font-semibold text-gray-500 mb-3">快捷操作</div>
            <div className="grid grid-cols-2 gap-2">
              {quickActions.map((action, index) => (
                <Link
                  key={index}
                  href={action.href}
                  className="flex flex-col items-center py-3 px-2 rounded-lg hover:bg-gray-50 transition-colors group"
                >
                  <i className={`${action.icon} text-lg text-gray-400 group-hover:text-blue-500 mb-1`}></i>
                  <span className="text-xs text-gray-600 group-hover:text-gray-900">{action.label}</span>
                </Link>
              ))}
            </div>
          </div>

          {/* 底部用户信息 */}
          <div className="p-4 border-t border-gray-100 mt-auto">
            <div className="flex items-center">
              <div className="w-10 h-10 bg-gradient-to-r from-green-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-3">
                李
              </div>
              <div className="flex-1">
                <div className="font-medium text-gray-900 text-sm">李明</div>
                <div className="text-xs text-gray-500">管理员</div>
              </div>
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <i className="ri-more-fill"></i>
              </button>
            </div>
          </div>
        </div>

        {/* 主内容区域 */}
        <div className="flex-1 flex flex-col min-h-screen">
          {/* 顶部导航 */}
          <header className="bg-white/80 backdrop-blur-sm shadow-sm border-b border-gray-100 px-8 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">仪表盘</h1>
                <p className="text-sm text-gray-500 mt-1">管理您的数字资产和交易</p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <button className="p-2 text-gray-400 hover:text-gray-600 relative">
                    <i className="ri-notification-3-line text-xl"></i>
                    <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                  </button>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900"><EMAIL></div>
                  <div className="text-xs text-gray-500">上次登录: 2024-12-19 14:30</div>
                </div>
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-bold cursor-pointer">
                  李
                </div>
              </div>
            </div>
          </header>

          {/* 主要内容 */}
          <main className="flex-1 overflow-auto">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}
