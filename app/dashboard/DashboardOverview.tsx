
'use client';

import { useState, useEffect } from 'react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, BarChart, Bar } from 'recharts';

export default function DashboardOverview() {
  const [counters, setCounters] = useState({
    totalVolume: 0,
    transactions: 0,
    activeUsers: 0,
    revenue: 0
  });

  useEffect(() => {
    const animateCounters = () => {
      const duration = 2000;
      const steps = 50;
      const increment = duration / steps;
      
      const targets = { totalVolume: 1250000, transactions: 45632, activeUsers: 12847, revenue: 89500 };
      
      let step = 0;
      const timer = setInterval(() => {
        step++;
        const progress = step / steps;
        
        setCounters({
          totalVolume: Math.floor(targets.totalVolume * progress),
          transactions: Math.floor(targets.transactions * progress),
          activeUsers: Math.floor(targets.activeUsers * progress),
          revenue: Math.floor(targets.revenue * progress)
        });
        
        if (step >= steps) {
          clearInterval(timer);
          setCounters(targets);
        }
      }, increment);
    };
    
    animateCounters();
  }, []);

  const volumeData = [
    { name: '1月', value: 120000 },
    { name: '2月', value: 190000 },
    { name: '3月', value: 180000 },
    { name: '4月', value: 220000 },
    { name: '5月', value: 250000 },
    { name: '6月', value: 280000 },
    { name: '7月', value: 320000 }
  ];

  const transactionData = [
    { name: '周一', value: 4500 },
    { name: '周二', value: 5200 },
    { name: '周三', value: 4800 },
    { name: '周四', value: 6100 },
    { name: '周五', value: 7200 },
    { name: '周六', value: 5800 },
    { name: '周日', value: 4900 }
  ];

  const revenueData = [
    { name: '1月', value: 8500 },
    { name: '2月', value: 12000 },
    { name: '3月', value: 11500 },
    { name: '4月', value: 15200 },
    { name: '5月', value: 18000 },
    { name: '6月', value: 22000 },
    { name: '7月', value: 25800 }
  ];

  const stats = [
    {
      title: '交易总量',
      value: `$${counters.totalVolume.toLocaleString()}`,
      change: '+12.5%',
      changeType: 'positive',
      icon: 'ri-line-chart-line',
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50'
    },
    {
      title: '交易笔数',
      value: counters.transactions.toLocaleString(),
      change: '+8.3%',
      changeType: 'positive',
      icon: 'ri-exchange-line',
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: '活跃用户',
      value: counters.activeUsers.toLocaleString(),
      change: '+15.2%',
      changeType: 'positive',
      icon: 'ri-user-line',
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50'
    },
    {
      title: '总收入',
      value: `$${counters.revenue.toLocaleString()}`,
      change: '+22.1%',
      changeType: 'positive',
      icon: 'ri-money-dollar-circle-line',
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50'
    }
  ];

  const recentTransactions = [
    { id: 'TX001', type: 'buy', amount: '$2,500', crypto: 'BTC', status: 'completed', time: '2分钟前' },
    { id: 'TX002', type: 'sell', amount: '$1,200', crypto: 'ETH', status: 'completed', time: '5分钟前' },
    { id: 'TX003', type: 'buy', amount: '$800', crypto: 'USDT', status: 'pending', time: '8分钟前' },
    { id: 'TX004', type: 'sell', amount: '$3,200', crypto: 'BTC', status: 'completed', time: '12分钟前' },
    { id: 'TX005', type: 'buy', amount: '$650', crypto: 'ADA', status: 'completed', time: '18分钟前' }
  ];

  return (
    <div className="space-y-8">
      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <div key={index} className="group relative">
            <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl"></div>
            <div className={`relative p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 ${stat.bgColor} group-hover:bg-white/90`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-xl flex items-center justify-center shadow-lg`}>
                  <i className={`${stat.icon} text-white text-xl`}></i>
                </div>
                <span className={`text-sm font-medium px-2 py-1 rounded-full ${
                  stat.changeType === 'positive' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                }`}>
                  {stat.change}
                </span>
              </div>
              <h3 className="text-gray-600 text-sm mb-2">{stat.title}</h3>
              <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
            </div>
          </div>
        ))}
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 交易量趋势 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">交易量趋势</h3>
            <div className="flex items-center text-sm text-gray-500">
              <i className="ri-calendar-line w-4 h-4 flex items-center justify-center mr-1"></i>
              最近7个月
            </div>
          </div>
          <ResponsiveContainer width="100%" height={280}>
            <AreaChart data={volumeData}>
              <defs>
                <linearGradient id="volumeGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.8}/>
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.1}/>
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis dataKey="name" stroke="#6B7280" />
              <YAxis stroke="#6B7280" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                  border: '1px solid #E5E7EB',
                  borderRadius: '12px',
                  boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Area 
                type="monotone" 
                dataKey="value" 
                stroke="#3B82F6" 
                fillOpacity={1} 
                fill="url(#volumeGradient)"
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* 每日交易数 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">每日交易数</h3>
            <div className="flex items-center text-sm text-gray-500">
              <i className="ri-calendar-line w-4 h-4 flex items-center justify-center mr-1"></i>
              本周
            </div>
          </div>
          <ResponsiveContainer width="100%" height={280}>
            <BarChart data={transactionData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis dataKey="name" stroke="#6B7280" />
              <YAxis stroke="#6B7280" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                  border: '1px solid #E5E7EB',
                  borderRadius: '12px',
                  boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Bar dataKey="value" fill="#10B981" radius={[4, 4, 0, 0]} />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* 收入趋势和最近交易 */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* 收入趋势 */}
        <div className="lg:col-span-2 bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">收入趋势</h3>
            <div className="flex items-center text-sm text-gray-500">
              <i className="ri-calendar-line w-4 h-4 flex items-center justify-center mr-1"></i>
              最近7个月
            </div>
          </div>
          <ResponsiveContainer width="100%" height={280}>
            <LineChart data={revenueData}>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis dataKey="name" stroke="#6B7280" />
              <YAxis stroke="#6B7280" />
              <Tooltip 
                contentStyle={{ 
                  backgroundColor: 'rgba(255, 255, 255, 0.95)', 
                  border: '1px solid #E5E7EB',
                  borderRadius: '12px',
                  boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)'
                }}
              />
              <Line 
                type="monotone" 
                dataKey="value" 
                stroke="#8B5CF6" 
                strokeWidth={3}
                dot={{ fill: '#8B5CF6', strokeWidth: 2, r: 4 }}
                activeDot={{ r: 6, fill: '#8B5CF6' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* 最近交易 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-xl">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">最近交易</h3>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium cursor-pointer">
              查看全部
            </button>
          </div>
          <div className="space-y-4">
            {recentTransactions.map((tx, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200">
                <div className="flex items-center space-x-3">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    tx.type === 'buy' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                  }`}>
                    <i className={`${tx.type === 'buy' ? 'ri-arrow-down-line' : 'ri-arrow-up-line'} text-sm`}></i>
                  </div>
                  <div>
                    <div className="font-medium text-gray-900">{tx.amount}</div>
                    <div className="text-sm text-gray-500">{tx.crypto}</div>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-sm px-2 py-1 rounded-full ${
                    tx.status === 'completed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {tx.status === 'completed' ? '已完成' : '处理中'}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">{tx.time}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}