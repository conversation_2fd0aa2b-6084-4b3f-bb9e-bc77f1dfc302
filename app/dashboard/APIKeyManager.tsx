
'use client';

import { useState } from 'react';

export default function APIKeyManager() {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [newKeyName, setNewKeyName] = useState('');
  const [newKeyPermissions, setNewKeyPermissions] = useState<string[]>([]);

  const apiKeys = [
    {
      id: 'key_1',
      name: '生产环境密钥',
      key: 'pk_live_1234567890abcdef',
      permissions: ['read', 'write', 'delete'],
      created: '2024-07-20',
      lastUsed: '2024-07-26 14:30',
      status: 'active'
    },
    {
      id: 'key_2',
      name: '测试环境密钥',
      key: 'pk_test_abcdef1234567890',
      permissions: ['read', 'write'],
      created: '2024-07-15',
      lastUsed: '2024-07-25 10:15',
      status: 'active'
    },
    {
      id: 'key_3',
      name: '只读密钥',
      key: 'pk_read_0987654321fedcba',
      permissions: ['read'],
      created: '2024-07-10',
      lastUsed: '从未使用',
      status: 'inactive'
    }
  ];

  const permissions = [
    { id: 'read', name: '读取', description: '查看账户信息和交易记录' },
    { id: 'write', name: '写入', description: '创建交易和更新账户信息' },
    { id: 'delete', name: '删除', description: '删除交易记录和账户数据' }
  ];

  const handleCreateKey = () => {
    if (newKeyName.trim() && newKeyPermissions.length > 0) {
      // 这里应该调用API创建新密钥
      console.log('Creating new API key:', { name: newKeyName, permissions: newKeyPermissions });
      setShowCreateModal(false);
      setNewKeyName('');
      setNewKeyPermissions([]);
    }
  };

  const handlePermissionChange = (permissionId: string, checked: boolean) => {
    if (checked) {
      setNewKeyPermissions([...newKeyPermissions, permissionId]);
    } else {
      setNewKeyPermissions(newKeyPermissions.filter(p => p !== permissionId));
    }
  };

  const maskApiKey = (key: string) => {
    return key.substring(0, 12) + '...' + key.substring(key.length - 4);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900">API密钥管理</h1>
        <button 
          onClick={() => setShowCreateModal(true)}
          className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          创建新密钥
        </button>
      </div>

      {/* API使用概览 */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">API使用概览</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-blue-600">3</div>
            <div className="text-sm text-gray-600">活跃密钥</div>
          </div>
          <div className="bg-green-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-green-600">12,567</div>
            <div className="text-sm text-gray-600">本月请求</div>
          </div>
          <div className="bg-yellow-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-yellow-600">99.9%</div>
            <div className="text-sm text-gray-600">成功率</div>
          </div>
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="text-2xl font-bold text-purple-600">45ms</div>
            <div className="text-sm text-gray-600">平均响应时间</div>
          </div>
        </div>
      </div>

      {/* API密钥列表 */}
      <div className="bg-white rounded-lg shadow-sm">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">API密钥列表</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  名称
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  密钥
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  权限
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  创建时间
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  最后使用
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {apiKeys.map((key) => (
                <tr key={key.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <i className="ri-key-line text-blue-600"></i>
                      </div>
                      <div className="text-sm font-medium text-gray-900">{key.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <span className="text-sm text-gray-900 font-mono">{maskApiKey(key.key)}</span>
                      <button className="ml-2 text-gray-400 hover:text-gray-600">
                        <i className="ri-file-copy-line"></i>
                      </button>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-1">
                      {key.permissions.map((perm) => (
                        <span key={perm} className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                          {perm}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {key.created}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {key.lastUsed}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      key.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {key.status === 'active' ? '活跃' : '未激活'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex space-x-2">
                      <button className="text-blue-600 hover:text-blue-900">编辑</button>
                      <button className="text-red-600 hover:text-red-900">删除</button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* 创建新密钥弹窗 */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">创建新API密钥</h3>
              <button 
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <i className="ri-close-line"></i>
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  密钥名称
                </label>
                <input
                  type="text"
                  value={newKeyName}
                  onChange={(e) => setNewKeyName(e.target.value)}
                  className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入密钥名称"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  权限设置
                </label>
                <div className="space-y-2">
                  {permissions.map((perm) => (
                    <label key={perm.id} className="flex items-start">
                      <input
                        type="checkbox"
                        checked={newKeyPermissions.includes(perm.id)}
                        onChange={(e) => handlePermissionChange(perm.id, e.target.checked)}
                        className="mt-1 mr-3"
                      />
                      <div>
                        <div className="text-sm font-medium text-gray-900">{perm.name}</div>
                        <div className="text-xs text-gray-500">{perm.description}</div>
                      </div>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => setShowCreateModal(false)}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleCreateKey}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                创建密钥
              </button>
            </div>
          </div>
        </div>
      )}

      {/* API文档链接 */}
      <div className="bg-white rounded-lg shadow-sm p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">开发者资源</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="border border-gray-200 rounded-lg p-4">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center mb-3">
              <i className="ri-book-open-line text-blue-600"></i>
            </div>
            <h3 className="text-base font-medium text-gray-900 mb-2">API文档</h3>
            <p className="text-sm text-gray-600 mb-3">详细的API接口文档和使用指南</p>
            <button className="text-blue-600 hover:text-blue-700 text-sm">查看文档</button>
          </div>

          <div className="border border-gray-200 rounded-lg p-4">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center mb-3">
              <i className="ri-code-line text-green-600"></i>
            </div>
            <h3 className="text-base font-medium text-gray-900 mb-2">代码示例</h3>
            <p className="text-sm text-gray-600 mb-3">多种编程语言的示例代码</p>
            <button className="text-blue-600 hover:text-blue-700 text-sm">查看示例</button>
          </div>

          <div className="border border-gray-200 rounded-lg p-4">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center mb-3">
              <i className="ri-tools-line text-purple-600"></i>
            </div>
            <h3 className="text-base font-medium text-gray-900 mb-2">测试工具</h3>
            <p className="text-sm text-gray-600 mb-3">API测试工具和调试环境</p>
            <button className="text-blue-600 hover:text-blue-700 text-sm">使用工具</button>
          </div>
        </div>
      </div>
    </div>
  );
}
