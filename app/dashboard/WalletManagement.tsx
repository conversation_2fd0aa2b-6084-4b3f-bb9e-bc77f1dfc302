
'use client';

import { useState, useCallback, useMemo } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

// TypeScript interfaces
interface Wallet {
  name: string;
  symbol: string;
  balance: string;
  value: string;
  change: string;
  changeType: 'positive' | 'negative';
  logo: string;
  address: string;
  network: string;
  color: string;
}

export default function WalletManagement() {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState('overview');
  const [imageErrors, setImageErrors] = useState<Record<string, boolean>>({});

  const wallets = [
    {
      name: 'Bitcoin',
      symbol: 'BTC',
      balance: '0.08245',
      value: '$2,487.50',
      change: '+5.2%',
      changeType: 'positive',
      logo: 'https://cryptologos.cc/logos/bitcoin-btc-logo.png',
      address: '1A2B3C4D5E6F7G8H9I0J1K2L3M4N5O6P7Q8R9S0T',
      network: 'Bitcoin',
      color: 'from-orange-500 to-orange-600'
    },
    {
      name: 'Ethereum',
      symbol: 'ETH',
      balance: '1.2543',
      value: '$3,156.80',
      change: '+3.8%',
      changeType: 'positive',
      logo: 'https://cryptologos.cc/logos/ethereum-eth-logo.png',
      address: '******************************************',
      network: 'Ethereum',
      color: 'from-blue-500 to-blue-600'
    },
    {
      name: 'Tether',
      symbol: 'USDT',
      balance: '5,000.00',
      value: '$5,000.00',
      change: '+0.01%',
      changeType: 'positive',
      logo: 'https://cryptologos.cc/logos/tether-usdt-logo.png',
      address: '0xabcdefghijklmnopqrstuvwxyz1234567890abcdef',
      network: 'Ethereum',
      color: 'from-green-500 to-green-600'
    },
    {
      name: 'Cardano',
      symbol: 'ADA',
      balance: '2,500.00',
      value: '$1,250.00',
      change: '-2.1%',
      changeType: 'negative',
      logo: 'https://cryptologos.cc/logos/cardano-ada-logo.png',
      address: 'addr1qxy2fxv2umyhttkxyxp8x0dlpdt3k6cwng5pxj3jhsydzer',
      network: 'Cardano',
      color: 'from-purple-500 to-purple-600'
    }
  ];

  const recentActivity = [
    { type: 'receive', amount: '0.05 BTC', value: '$1,500.00', time: '2小时前', status: 'confirmed' },
    { type: 'send', amount: '0.8 ETH', value: '$2,000.00', time: '5小时前', status: 'confirmed' },
    { type: 'receive', amount: '1,000 USDT', value: '$1,000.00', time: '1天前', status: 'confirmed' },
    { type: 'send', amount: '500 ADA', value: '$250.00', time: '2天前', status: 'confirmed' }
  ];

  const totalValue = wallets.reduce((sum, wallet) => sum + parseFloat(wallet.value.replace('$', '').replace(',', '')), 0);

  return (
    <div className="space-y-8">
      {/* 总览卡片 */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 rounded-3xl p-8 text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(255,255,255,0.15),transparent_70%)]"></div>
        <div className="absolute top-4 right-4 w-32 h-32 bg-white/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-4 left-4 w-24 h-24 bg-white/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        
        <div className="relative z-10">
          <div className="flex justify-between items-start mb-8">
            <div>
              <h2 className="text-3xl font-bold mb-2">钱包总览</h2>
              <p className="text-blue-100">管理您的数字资产</p>
            </div>
            <div className="text-right">
              <div className="text-4xl font-bold mb-2">${totalValue.toLocaleString()}</div>
              <div className="text-blue-100">总价值</div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <i className="ri-wallet-line text-2xl"></i>
                <span className="text-sm bg-white/20 px-3 py-1 rounded-full">4 钱包</span>
              </div>
              <div className="text-xl font-bold mb-1">{wallets.length}</div>
              <div className="text-blue-100 text-sm">活跃钱包</div>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <i className="ri-arrow-up-line text-2xl"></i>
                <span className="text-sm bg-white/20 px-3 py-1 rounded-full">+12.5%</span>
              </div>
              <div className="text-xl font-bold mb-1">$1,250.00</div>
              <div className="text-blue-100 text-sm">24h 变化</div>
            </div>
            
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="flex items-center justify-between mb-4">
                <i className="ri-exchange-line text-2xl"></i>
                <span className="text-sm bg-white/20 px-3 py-1 rounded-full">156</span>
              </div>
              <div className="text-xl font-bold mb-1">156</div>
              <div className="text-blue-100 text-sm">总交易数</div>
            </div>
          </div>
        </div>
      </div>

      {/* 钱包列表 */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {wallets.map((wallet, index) => (
          <div key={index} className="group relative">
            <div className="absolute inset-0 bg-gradient-to-r opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl blur-xl"></div>
            <div className="relative bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  <img 
                    src={wallet.logo}
                    alt={wallet.name}
                    className="w-12 h-12 rounded-full"
                    onError={(e) => {
                      e.currentTarget.src = `https://readdy.ai/api/search-image?query=$%7Bwallet.name%7D%20cryptocurrency%20logo%20icon&width=48&height=48&seq=${wallet.symbol}&orientation=squarish`;
                    }}
                  />
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">{wallet.name}</h3>
                    <p className="text-gray-500">{wallet.symbol}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900">{wallet.balance}</div>
                  <div className="text-gray-500">{wallet.symbol}</div>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-4 mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-gray-600">价值</span>
                  <span className="text-xl font-bold text-gray-900">{wallet.value}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">24h 变化</span>
                  <span className={`font-medium ${
                    wallet.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {wallet.change}
                  </span>
                </div>
              </div>
              
              <div className="mb-6">
                <div className="text-sm text-gray-600 mb-2">钱包地址</div>
                <div className="flex items-center justify-between bg-gray-100 rounded-lg p-3">
                  <span className="text-sm text-gray-800 font-mono truncate">
                    {wallet.address}
                  </span>
                  <button className="ml-2 p-1 text-gray-500 hover:text-gray-700 cursor-pointer">
                    <i className="ri-file-copy-line"></i>
                  </button>
                </div>
              </div>
              
              <div className="flex space-x-3">
                <button className={`flex-1 bg-gradient-to-r ${wallet.color} text-white py-3 px-4 rounded-xl font-medium hover:opacity-90 transition-opacity duration-200 whitespace-nowrap cursor-pointer shadow-lg`}>
                  <i className="ri-send-plane-line w-4 h-4 flex items-center justify-center mr-2"></i>
                  发送
                </button>
                <button className="flex-1 border border-gray-200 text-gray-700 py-3 px-4 rounded-xl font-medium hover:bg-gray-50 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  <i className="ri-qr-code-line w-4 h-4 flex items-center justify-center mr-2"></i>
                  接收
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 最近活动 */}
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-gray-100 shadow-xl">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-bold text-gray-900">最近活动</h3>
          <button className="text-blue-600 hover:text-blue-700 font-medium cursor-pointer">
            查看全部
          </button>
        </div>
        
        <div className="space-y-4">
          {recentActivity.map((activity, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-200">
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                  activity.type === 'receive' ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
                }`}>
                  <i className={`${activity.type === 'receive' ? 'ri-arrow-down-line' : 'ri-arrow-up-line'} text-xl`}></i>
                </div>
                <div>
                  <div className="font-medium text-gray-900">
                    {activity.type === 'receive' ? '接收' : '发送'} {activity.amount}
                  </div>
                  <div className="text-sm text-gray-500">{activity.time}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-gray-900">{activity.value}</div>
                <div className="text-sm text-green-600">{activity.status === 'confirmed' ? '已确认' : '待确认'}</div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
