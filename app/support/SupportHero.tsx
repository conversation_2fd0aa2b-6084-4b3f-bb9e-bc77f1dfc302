
'use client';

import { useState } from 'react';

export default function SupportHero() {
  const [searchQuery, setSearchQuery] = useState('');

  const supportOptions = [
    {
      title: '技术支持',
      description: '获取技术问题的专业帮助',
      icon: 'ri-customer-service-line',
      color: 'from-blue-500 to-blue-600',
      availability: '24/7',
      responseTime: '< 2小时'
    },
    {
      title: '开发者支持',
      description: 'API集成和开发相关问题',
      icon: 'ri-code-line',
      color: 'from-green-500 to-green-600',
      availability: '工作日',
      responseTime: '< 4小时'
    },
    {
      title: '账户问题',
      description: '账户设置和安全相关',
      icon: 'ri-user-settings-line',
      color: 'from-purple-500 to-purple-600',
      availability: '24/7',
      responseTime: '< 1小时'
    },
    {
      title: '合规咨询',
      description: '法律法规和合规问题',
      icon: 'ri-shield-check-line',
      color: 'from-orange-500 to-orange-600',
      availability: '工作日',
      responseTime: '< 24小时'
    }
  ];

  const quickLinks = [
    { title: '入门指南', icon: 'ri-book-open-line', category: '新手' },
    { title: 'API文档', icon: 'ri-terminal-line', category: '开发' },
    { title: '安全设置', icon: 'ri-shield-line', category: '安全' },
    { title: '费用说明', icon: 'ri-money-dollar-circle-line', category: '计费' },
    { title: '故障排除', icon: 'ri-tools-line', category: '问题' },
    { title: '状态页面', icon: 'ri-pulse-line', category: '状态' }
  ];

  const popularTopics = [
    '如何创建API密钥？',
    '支付失败怎么办？',
    '如何设置webhook？',
    '手续费怎么计算？',
    '如何联系客服？'
  ];

  return (
    <section className="py-28 bg-white relative overflow-hidden">
      {/* 高级装饰背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/30 to-purple-200/30 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-80 h-80 bg-gradient-to-r from-green-200/30 to-blue-200/30 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gradient-to-r from-purple-200/30 to-pink-200/30 rounded-full blur-3xl animate-pulse delay-2000"></div>
      </div>
      
      {/* 动态网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.05)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.05)_1px,transparent_1px)] bg-[size:80px_80px]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8 shadow-lg">
            <i className="ri-customer-service-line mr-2"></i>
            我们随时为您服务
          </div>
          
          <h1 className="text-7xl md:text-8xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8 leading-tight">
            帮助中心
          </h1>
          
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-12">
            寻找答案？我们的专业支持团队和丰富的知识库随时为您提供帮助。
            快速找到解决方案，让您的业务持续运行。
          </p>
          
          {/* 搜索栏 */}
          <div className="max-w-2xl mx-auto mb-12">
            <div className="relative">
              <input
                type="text"
                placeholder="搜索帮助文档、常见问题..."
                className="w-full px-8 py-6 pl-16 text-lg text-gray-700 bg-white border-2 border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 shadow-xl"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <i className="ri-search-line absolute left-6 top-1/2 transform -translate-y-1/2 text-gray-400 text-2xl"></i>
              <button className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-6 py-3 rounded-xl font-medium hover:from-blue-600 hover:to-purple-600 transition-all duration-200 cursor-pointer">
                搜索
              </button>
            </div>
          </div>
          
          {/* 热门搜索 */}
          <div className="flex flex-wrap justify-center gap-3 mb-16">
            <span className="text-gray-600 text-sm">热门搜索:</span>
            {popularTopics.map((topic, index) => (
              <button
                key={index}
                className="text-blue-600 hover:text-blue-800 text-sm bg-blue-50 hover:bg-blue-100 px-3 py-1 rounded-full transition-all duration-200 cursor-pointer"
              >
                {topic}
              </button>
            ))}
          </div>
        </div>
        
        {/* 支持选项 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-20">
          {supportOptions.map((option, index) => (
            <div key={index} className="group relative">
              <div className={`absolute inset-0 bg-gradient-to-r ${option.color} opacity-0 group-hover:opacity-20 transition-opacity duration-300 rounded-2xl blur-xl`}></div>
              <div className="relative bg-white rounded-2xl p-8 shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 cursor-pointer">
                <div className={`w-16 h-16 bg-gradient-to-r ${option.color} rounded-2xl flex items-center justify-center mx-auto mb-6`}>
                  <i className={`${option.icon} text-white text-2xl`}></i>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-3 text-center">{option.title}</h3>
                <p className="text-gray-600 text-center mb-6">{option.description}</p>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">服务时间</span>
                    <span className="font-medium text-gray-900">{option.availability}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-500">响应时间</span>
                    <span className="font-medium text-gray-900">{option.responseTime}</span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {/* 快速链接 */}
        <div className="bg-white rounded-3xl p-8 shadow-2xl border border-gray-100 mb-20">
          <h3 className="text-3xl font-bold text-gray-900 mb-8 text-center">
            快速链接
          </h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {quickLinks.map((link, index) => (
              <div key={index} className="group">
                <div className="flex flex-col items-center p-4 bg-gray-50 rounded-xl hover:bg-blue-50 transition-all duration-200 cursor-pointer">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-200">
                    <i className={`${link.icon} text-white text-xl`}></i>
                  </div>
                  <span className="text-sm font-medium text-gray-900 text-center">{link.title}</span>
                  <span className="text-xs text-gray-500 mt-1">{link.category}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
        
        {/* 联系我们 */}
        <div className="text-center bg-gradient-to-r from-gray-50 to-blue-50 rounded-3xl p-12 border border-gray-200">
          <h3 className="text-4xl font-bold text-gray-900 mb-6">
            仍需要帮助？
          </h3>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            如果您无法找到所需的答案，我们的专业支持团队随时准备为您提供个性化帮助。
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap cursor-pointer">
              <i className="ri-chat-smile-line mr-2"></i>
              在线聊天
            </button>
            <button className="border-2 border-gray-200 text-gray-700 px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-50 transition-all duration-300 whitespace-nowrap cursor-pointer">
              <i className="ri-mail-line mr-2"></i>
              发送邮件
            </button>
          </div>
          
          {/* 服务时间 */}
          <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6 text-center">
            <div className="p-4 bg-white rounded-xl shadow-md">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                <i className="ri-chat-smile-line text-white text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">在线聊天</h4>
              <p className="text-gray-600 text-sm">24/7 即时回复</p>
            </div>
            <div className="p-4 bg-white rounded-xl shadow-md">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                <i className="ri-mail-line text-white text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">邮件支持</h4>
              <p className="text-gray-600 text-sm">4小时内回复</p>
            </div>
            <div className="p-4 bg-white rounded-xl shadow-md">
              <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-3">
                <i className="ri-phone-line text-white text-xl"></i>
              </div>
              <h4 className="font-semibold text-gray-900 mb-1">电话支持</h4>
              <p className="text-gray-600 text-sm">工作日 9:00-18:00</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
