'use client';

import { useState } from 'react';

export default function FAQSection() {
  const [activeCategory, setActiveCategory] = useState('general');
  const [openFAQ, setOpenFAQ] = useState(null);

  const categories = [
    { id: 'general', name: '常见问题', icon: 'ri-question-line' },
    { id: 'payment', name: '支付相关', icon: 'ri-money-dollar-circle-line' },
    { id: 'integration', name: '集成问题', icon: 'ri-code-line' },
    { id: 'security', name: '安全问题', icon: 'ri-shield-line' },
    { id: 'account', name: '账户管理', icon: 'ri-user-line' }
  ];

  const faqs = {
    general: [
      {
        question: '什么是加密货币支付？',
        answer: '加密货币支付是使用比特币、以太坊等数字货币进行的交易支付方式。它具有去中心化、快速、低成本的特点。'
      },
      {
        question: '如何开始使用我们的服务？',
        answer: '您只需要注册账户，完成身份验证，获取API密钥，就可以开始集成我们的支付服务。'
      },
      {
        question: '支持哪些加密货币？',
        answer: '我们支持Bitcoin (BTC)、Ethereum (ETH)、Tether (USDT)、Binance Coin (BNB)等主流加密货币。'
      }
    ],
    payment: [
      {
        question: '支付处理需要多长时间？',
        answer: '比特币支付通常需要10-60分钟确认，以太坊支付需要2-15分钟，具体时间取决于网络拥堵情况。'
      },
      {
        question: '手续费是多少？',
        answer: '我们的手续费为2.5%，包含网络手续费和平台服务费。大额交易可享受更优惠的费率。'
      },
      {
        question: '如何处理支付失败？',
        answer: '如果支付失败，资金会自动退回到原支付账户。您可以在控制台查看详细的失败原因。'
      }
    ],
    integration: [
      {
        question: '如何集成API？',
        answer: '您可以使用我们提供的REST API或者官方SDK来集成支付功能。详细的集成指南请参考开发者文档。'
      },
      {
        question: '测试环境如何使用？',
        answer: '我们提供完整的测试环境，您可以使用测试API密钥进行开发和测试，无需真实的加密货币。'
      },
      {
        question: 'Webhook如何配置？',
        answer: '在控制台中配置Webhook URL，我们会向您的服务器发送支付状态更新通知。'
      }
    ],
    security: [
      {
        question: '如何保护API密钥？',
        answer: 'API密钥应该保存在服务器端，不要在客户端代码中暴露。定期更换密钥以提高安全性。'
      },
      {
        question: '资金如何保障安全？',
        answer: '我们使用多重签名钱包、冷存储等技术保护资金安全，并购买了保险以应对意外情况。'
      },
      {
        question: '如何启用二次验证？',
        answer: '在账户设置中启用Google Authenticator或短信验证，为您的账户添加额外的安全保护。'
      }
    ],
    account: [
      {
        question: '如何完成身份验证？',
        answer: '上传身份证件照片并进行人脸识别验证。通常在1-2个工作日内完成审核。'
      },
      {
        question: '忘记密码怎么办？',
        answer: '点击登录页面的"忘记密码"链接，通过邮箱或手机号重置密码。'
      },
      {
        question: '如何修改账户信息？',
        answer: '在账户设置页面可以修改基本信息，重要信息修改需要身份验证。'
      }
    ]
  };

  const toggleFAQ = (index) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            常见问题
          </h2>
          <p className="text-xl text-gray-600">
            查找您需要的答案
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-md p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">分类</h3>
              <div className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full flex items-center px-4 py-3 rounded-lg transition-colors duration-200 whitespace-nowrap cursor-pointer ${
                      activeCategory === category.id
                        ? 'bg-blue-600 text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                    }`}
                  >
                    <i className={`${category.icon} w-5 h-5 flex items-center justify-center mr-3`}></i>
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-3">
            <div className="bg-white rounded-xl shadow-md p-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-6">
                {categories.find(c => c.id === activeCategory)?.name}
              </h3>
              <div className="space-y-4">
                {faqs[activeCategory].map((faq, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg">
                    <button
                      onClick={() => toggleFAQ(index)}
                      className="w-full flex items-center justify-between p-4 text-left hover:bg-gray-50 transition-colors duration-200 cursor-pointer"
                    >
                      <span className="font-medium text-gray-900">{faq.question}</span>
                      <i className={`ri-arrow-${openFAQ === index ? 'up' : 'down'}-s-line text-gray-500 text-xl`}></i>
                    </button>
                    {openFAQ === index && (
                      <div className="px-4 pb-4 border-t border-gray-200">
                        <p className="text-gray-600 pt-4">{faq.answer}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}