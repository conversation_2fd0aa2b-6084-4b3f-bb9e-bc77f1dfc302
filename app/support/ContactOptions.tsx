'use client';

export default function ContactOptions() {
  const contactMethods = [
    {
      title: '在线客服',
      description: '24/7 在线支持，即时回复',
      availability: '全天候',
      response: '< 2分钟',
      icon: 'ri-message-3-line',
      color: 'bg-blue-600',
      action: '开始聊天'
    },
    {
      title: '邮件支持',
      description: '发送详细问题到我们的邮箱',
      availability: '工作日',
      response: '< 4小时',
      icon: 'ri-mail-line',
      color: 'bg-green-600',
      action: '发送邮件'
    },
    {
      title: '电话支持',
      description: '直接与技术专家通话',
      availability: '工作日 9:00-18:00',
      response: '即时',
      icon: 'ri-phone-line',
      color: 'bg-purple-600',
      action: '拨打电话'
    },
    {
      title: '技术支持',
      description: '专业的API和集成支持',
      availability: '工作日',
      response: '< 2小时',
      icon: 'ri-code-line',
      color: 'bg-orange-600',
      action: '提交工单'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            联系我们
          </h2>
          <p className="text-xl text-gray-600">
            选择最适合您的联系方式
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {contactMethods.map((method, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
              <div className="text-center">
                <div className={`w-16 h-16 ${method.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <i className={`${method.icon} text-white text-2xl`}></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {method.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {method.description}
                </p>
                
                <div className="space-y-2 mb-6">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">服务时间</span>
                    <span className="text-gray-700">{method.availability}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">响应时间</span>
                    <span className="text-gray-700">{method.response}</span>
                  </div>
                </div>
                
                <button className={`w-full ${method.color} text-white py-3 px-4 rounded-lg hover:opacity-90 transition-opacity duration-200 whitespace-nowrap cursor-pointer`}>
                  {method.action}
                </button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 bg-gray-50 rounded-xl p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                办公地址
              </h3>
              <div className="space-y-4">
                <div className="flex items-start">
                  <i className="ri-map-pin-line text-gray-400 text-xl mr-3 mt-1"></i>
                  <div>
                    <h4 className="font-medium text-gray-900">总部</h4>
                    <p className="text-gray-600">新加坡滨海湾金融中心</p>
                    <p className="text-gray-600">1 Marina Bay Financial Centre, Tower 1</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <i className="ri-map-pin-line text-gray-400 text-xl mr-3 mt-1"></i>
                  <div>
                    <h4 className="font-medium text-gray-900">美国办事处</h4>
                    <p className="text-gray-600">纽约曼哈顿金融区</p>
                    <p className="text-gray-600">200 West Street, New York, NY 10282</p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <i className="ri-map-pin-line text-gray-400 text-xl mr-3 mt-1"></i>
                  <div>
                    <h4 className="font-medium text-gray-900">欧洲办事处</h4>
                    <p className="text-gray-600">伦敦金融城</p>
                    <p className="text-gray-600">1 King William Street, London EC4N 7AF</p>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">
                快速链接
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-book-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    开发者文档
                  </a>
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-github-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    GitHub 仓库
                  </a>
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-discord-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    Discord 社区
                  </a>
                </div>
                <div className="space-y-2">
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-file-text-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    API 状态
                  </a>
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-bug-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    问题反馈
                  </a>
                  <a href="#" className="flex items-center text-blue-600 hover:text-blue-700 text-sm">
                    <i className="ri-road-map-line w-4 h-4 flex items-center justify-center mr-2"></i>
                    产品路线图
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}