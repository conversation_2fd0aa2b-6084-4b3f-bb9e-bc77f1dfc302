
'use client';

import { useState } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

export default function DeveloperSection() {
  const { t } = useLanguage();
  const [activeLanguage, setActiveLanguage] = useState('javascript');

  const languages = [
    { id: 'javascript', name: 'JavaScript', icon: 'ri-javascript-line' },
    { id: 'python', name: 'Python', icon: 'ri-code-line' },
    { id: 'php', name: 'PHP', icon: 'ri-code-s-slash-line' },
    { id: 'ruby', name: 'Ruby', icon: 'ri-code-box-line' }
  ];

  const codeExamples = {
    javascript: `// Initialize CryptoPay SDK
const cryptoPay = new CryptoPay({
  apiKey: 'your-api-key',
  environment: 'sandbox'
});

// Create a payment
const payment = await cryptoPay.payments.create({
  amount: 100.00,
  currency: 'USD',
  cryptocurrency: 'BTC',
  description: 'Product purchase'
});

// Handle payment confirmation
payment.on('confirmed', (data) => {
  console.log('Payment confirmed:', data);
});`,
    python: `# Initialize CryptoPay SDK
import cryptopay

client = cryptopay.Client(
    api_key='your-api-key',
    environment='sandbox'
)

# Create a payment
payment = client.payments.create(
    amount=100.00,
    currency='USD',
    cryptocurrency='BTC',
    description='Product purchase'
)

print(f"Payment ID: {payment.id}")`,
    php: `<?php
// Initialize CryptoPay SDK
require_once 'vendor/autoload.php';

$cryptoPay = new CryptoPay\\Client([
    'apiKey' => 'your-api-key',
    'environment' => 'sandbox'
]);

// Create a payment
$payment = $cryptoPay->payments->create([
    'amount' => 100.00,
    'currency' => 'USD',
    'cryptocurrency' => 'BTC',
    'description' => 'Product purchase'
]);

echo "Payment ID: " . $payment->id;
?>`,
    ruby: `# Initialize CryptoPay SDK
require 'cryptopay'

client = CryptoPay::Client.new(
  api_key: 'your-api-key',
  environment: 'sandbox'
)

# Create a payment
payment = client.payments.create(
  amount: 100.00,
  currency: 'USD',
  cryptocurrency: 'BTC',
  description: 'Product purchase'
)

puts "Payment ID: #{payment.id}"`
  };

  const features = [
    {
      title: t('developers.features.restfulApi.title'),
      description: t('developers.features.restfulApi.description'),
      icon: 'ri-api-line',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t('developers.features.webhooks.title'),
      description: t('developers.features.webhooks.description'),
      icon: 'ri-notification-line',
      color: 'from-green-500 to-green-600'
    },
    {
      title: t('developers.features.sdks.title'),
      description: t('developers.features.sdks.description'),
      icon: 'ri-code-box-line',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t('developers.features.sandbox.title'),
      description: t('developers.features.sandbox.description'),
      icon: 'ri-test-tube-line',
      color: 'from-orange-500 to-orange-600'
    }
  ];

  const stats = [
    { value: '99.9%', label: t('developers.trustedByDevelopers.apiUptime') },
    { value: '<100ms', label: t('developers.trustedByDevelopers.responseTime') },
    { value: '50+', label: t('developers.trustedByDevelopers.apiEndpoints') },
    { value: '24/7', label: t('developers.trustedByDevelopers.developerSupport') }
  ];

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-blue-100 text-violet-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-code-line mr-2"></i>
            {t('developers.tagline')}
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-violet-800 to-blue-800 bg-clip-text text-transparent mb-8">
            {t('developers.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('developers.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          {/* 代码示例 */}
          <div className="bg-white border border-gray-200 rounded-3xl p-8 shadow-xl">
            <div className="flex items-center justify-between mb-8">
              <h3 className="text-2xl font-bold text-gray-900">{t('developers.codeExample.title')}</h3>
              <div className="flex gap-2">
                {languages.map((lang) => (
                  <button
                    key={lang.id}
                    onClick={() => setActiveLanguage(lang.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap cursor-pointer ${
                      activeLanguage === lang.id
                        ? 'bg-gradient-to-r from-violet-500 to-blue-500 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <i className={`${lang.icon} mr-2`}></i>
                    {lang.name}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="bg-gray-900 rounded-2xl p-6 overflow-hidden">
              <div className="flex items-center justify-between mb-4">
                <div className="flex gap-2">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <span className="text-gray-400 text-sm">
                  {languages.find(l => l.id === activeLanguage)?.name}
                </span>
              </div>
              <pre className="text-sm text-gray-300 font-mono leading-relaxed overflow-x-auto">
                <code>{codeExamples[activeLanguage]}</code>
              </pre>
            </div>
          </div>
          
          {/* 开发者工具 */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">{t('developers.tools')}</h3>
            {features.map((feature, index) => (
              <div key={index} className="group bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="flex items-start">
                  <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mr-6 group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${feature.icon} text-white text-xl`}></i>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900 mb-2">{feature.title}</h4>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}

            <div className="bg-gradient-to-r from-violet-500 to-blue-500 rounded-2xl p-8 text-white">
              <h4 className="text-xl font-bold mb-4">
                <i className="ri-rocket-line mr-2"></i>
                {t('developers.quickStart.title')}
              </h4>
              <p className="text-violet-100 mb-6">
                {t('developers.quickStart.description')}
              </p>
              <div className="flex gap-4">
                <button className="bg-white text-violet-600 px-6 py-3 rounded-xl font-medium hover:bg-gray-100 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  <i className="ri-book-open-line mr-2"></i>
                  {t('developers.quickStart.viewDocs')}
                </button>
                <button className="border border-white/30 text-white px-6 py-3 rounded-xl font-medium hover:bg-white/10 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                  <i className="ri-download-line mr-2"></i>
                  {t('developers.quickStart.downloadSDK')}
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* 统计数据 */}
        <div className="bg-white border border-gray-200 rounded-3xl p-12 shadow-xl">
          <div className="text-center mb-12">
            <h3 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-violet-800 bg-clip-text text-transparent mb-6">
              {t('developers.trustedByDevelopers.title')}
            </h3>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              {t('developers.trustedByDevelopers.description')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-6 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl hover:shadow-lg transition-all duration-300">
                <div className="text-4xl font-bold bg-gradient-to-r from-violet-600 to-blue-600 bg-clip-text text-transparent mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
