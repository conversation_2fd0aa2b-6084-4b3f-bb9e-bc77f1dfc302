
'use client';

import { useState, useMemo, useCallback } from 'react';
import { useLanguage } from '../contexts/LanguageContext';

// TypeScript interfaces for better type safety
interface CodeExamples {
  javascript: string;
  python: string;
  php: string;
  ruby: string;
}

interface Language {
  id: keyof CodeExamples;
  name: string;
  icon: string;
}

interface Feature {
  title: string;
  description: string;
  icon: string;
  color: string;
}

interface Stat {
  value: string;
  label: string;
}

export default function DeveloperSection() {
  const { t } = useLanguage();
  const [activeLanguage, setActiveLanguage] = useState<keyof CodeExamples>('javascript');

  // Memoized languages with proper typing
  const languages = useMemo<Language[]>(() => [
    { id: 'javascript', name: 'JavaScript', icon: 'ri-javascript-line' },
    { id: 'python', name: 'Python', icon: 'ri-code-line' },
    { id: 'php', name: '<PERSON><PERSON>', icon: 'ri-code-s-slash-line' },
    { id: 'ruby', name: '<PERSON>', icon: 'ri-code-box-line' }
  ], []);

  // Memoized code examples with internationalization
  const codeExamples = useMemo<CodeExamples>(() => ({
    javascript: `// ${t('developers.codeExample.initializeSDK') || 'Initialize CryptoPay SDK'}
const cryptoPay = new CryptoPay({
  apiKey: 'your-api-key',
  environment: 'sandbox'
});

// ${t('developers.codeExample.createPayment') || 'Create a payment'}
const payment = await cryptoPay.payments.create({
  amount: 100.00,
  currency: 'USD',
  cryptocurrency: 'BTC',
  description: 'Product purchase'
});

// ${t('developers.codeExample.handleConfirmation') || 'Handle payment confirmation'}
payment.on('confirmed', (data) => {
  console.log('Payment confirmed:', data);
});`,
    python: `# ${t('developers.codeExample.initializeSDK') || 'Initialize CryptoPay SDK'}
import cryptopay

client = cryptopay.Client(
    api_key='your-api-key',
    environment='sandbox'
)

# ${t('developers.codeExample.createPayment') || 'Create a payment'}
payment = client.payments.create(
    amount=100.00,
    currency='USD',
    cryptocurrency='BTC',
    description='Product purchase'
)

print(f"Payment ID: {payment.id}")`,
    php: `<?php
// ${t('developers.codeExample.initializeSDK') || 'Initialize CryptoPay SDK'}
require_once 'vendor/autoload.php';

$cryptoPay = new CryptoPay\\Client([
    'apiKey' => 'your-api-key',
    'environment' => 'sandbox'
]);

// ${t('developers.codeExample.createPayment') || 'Create a payment'}
$payment = $cryptoPay->payments->create([
    'amount' => 100.00,
    'currency' => 'USD',
    'cryptocurrency' => 'BTC',
    'description' => 'Product purchase'
]);

echo "Payment ID: " . $payment->id;
?>`,
    ruby: `# ${t('developers.codeExample.initializeSDK') || 'Initialize CryptoPay SDK'}
require 'cryptopay'

client = CryptoPay::Client.new(
  api_key: 'your-api-key',
  environment: 'sandbox'
)

# ${t('developers.codeExample.createPayment') || 'Create a payment'}
payment = client.payments.create(
  amount: 100.00,
  currency: 'USD',
  cryptocurrency: 'BTC',
  description: 'Product purchase'
)

puts "Payment ID: #{payment.id}"`
  }), [t]);

  // Memoized features with internationalization
  const features = useMemo<Feature[]>(() => [
    {
      title: t('developers.features.restfulApi.title') || 'RESTful API',
      description: t('developers.features.restfulApi.description') || 'Complete REST API with comprehensive documentation',
      icon: 'ri-api-line',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t('developers.features.webhooks.title') || 'Webhooks',
      description: t('developers.features.webhooks.description') || 'Real-time notifications for payment events',
      icon: 'ri-notification-line',
      color: 'from-green-500 to-green-600'
    },
    {
      title: t('developers.features.sdks.title') || 'SDKs',
      description: t('developers.features.sdks.description') || 'Official SDKs for popular programming languages',
      icon: 'ri-code-box-line',
      color: 'from-purple-500 to-purple-600'
    },
    {
      title: t('developers.features.sandbox.title') || 'Sandbox',
      description: t('developers.features.sandbox.description') || 'Test environment for development and integration',
      icon: 'ri-test-tube-line',
      color: 'from-orange-500 to-orange-600'
    }
  ], [t]);

  // Memoized stats with internationalization
  const stats = useMemo<Stat[]>(() => [
    {
      value: '99.9%',
      label: t('developers.trustedByDevelopers.apiUptime') || 'API Uptime'
    },
    {
      value: '<100ms',
      label: t('developers.trustedByDevelopers.responseTime') || 'Response Time'
    },
    {
      value: '50+',
      label: t('developers.trustedByDevelopers.apiEndpoints') || 'API Endpoints'
    },
    {
      value: '24/7',
      label: t('developers.trustedByDevelopers.developerSupport') || 'Developer Support'
    }
  ], [t]);

  // Safe language switching with validation
  const handleLanguageChange = useCallback((langId: string) => {
    const validLanguages: (keyof CodeExamples)[] = ['javascript', 'python', 'php', 'ruby'];
    if (validLanguages.includes(langId as keyof CodeExamples)) {
      setActiveLanguage(langId as keyof CodeExamples);
    } else {
      console.warn(`Invalid language: ${langId}`);
    }
  }, []);

  // Handle documentation button click
  const handleViewDocs = useCallback(() => {
    // Navigate to documentation page
    window.open('/developers', '_blank');
  }, []);

  // Handle SDK download button click
  const handleDownloadSDK = useCallback(() => {
    // Navigate to SDK download page
    window.open('https://github.com/cryptopay/sdks', '_blank');
  }, []);

  // Get current language name safely
  const currentLanguageName = useMemo(() => {
    const lang = languages.find(l => l.id === activeLanguage);
    return lang?.name || 'Unknown';
  }, [languages, activeLanguage]);
  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-violet-100 to-blue-100 text-violet-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-code-line mr-2" aria-hidden="true"></i>
            {t('developers.tagline') || 'For Developers'}
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-violet-800 to-blue-800 bg-clip-text text-transparent mb-8">
            {t('developers.title') || 'Developer Resources'}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('developers.subtitle') || 'Powerful APIs and tools to help your business grow'}
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-20">
          {/* 代码示例 */}
          <div className="bg-white border border-gray-200 rounded-3xl p-8 shadow-xl">
            <div className="flex items-center justify-between mb-8">
              <h3 className="text-2xl font-bold text-gray-900">
                {t('developers.codeExample.title') || 'Code Example'}
              </h3>
              <div
                className="flex gap-2"
                role="tablist"
                aria-label={t('developers.codeExample.languageTabsLabel') || 'Programming Language Selection'}
              >
                {languages.map((lang) => (
                  <button
                    key={lang.id}
                    onClick={() => handleLanguageChange(lang.id)}
                    role="tab"
                    aria-selected={activeLanguage === lang.id}
                    aria-controls={`code-panel-${lang.id}`}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap cursor-pointer focus:outline-none focus:ring-2 focus:ring-violet-500 focus:ring-offset-2 ${
                      activeLanguage === lang.id
                        ? 'bg-gradient-to-r from-violet-500 to-blue-500 text-white'
                        : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                    }`}
                  >
                    <i className={`${lang.icon} mr-2`} aria-hidden="true"></i>
                    {lang.name}
                  </button>
                ))}
              </div>
            </div>
            
            <div className="bg-gray-900 rounded-2xl p-6 overflow-hidden">
              <div className="flex items-center justify-between mb-4">
                <div className="flex gap-2" aria-hidden="true">
                  <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                </div>
                <span className="text-gray-400 text-sm">
                  {currentLanguageName}
                </span>
              </div>
              <div
                role="tabpanel"
                id={`code-panel-${activeLanguage}`}
                aria-labelledby={`tab-${activeLanguage}`}
              >
                <pre className="text-sm text-gray-300 font-mono leading-relaxed overflow-x-auto">
                  <code>{codeExamples[activeLanguage]}</code>
                </pre>
              </div>
            </div>
          </div>
          
          {/* 开发者工具 */}
          <div className="space-y-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-8">
              {t('developers.tools') || 'Developer Tools'}
            </h3>
            {features.map((feature, index) => (
              <div key={index} className="group bg-white border border-gray-200 rounded-2xl p-6 hover:shadow-lg transition-all duration-300">
                <div className="flex items-start">
                  <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mr-6 group-hover:scale-110 transition-transform duration-300`}>
                    <i className={`${feature.icon} text-white text-xl`} aria-hidden="true"></i>
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900 mb-2">{feature.title}</h4>
                    <p className="text-gray-600">{feature.description}</p>
                  </div>
                </div>
              </div>
            ))}

            <div className="bg-gradient-to-r from-violet-500 to-blue-500 rounded-2xl p-8 text-white">
              <h4 className="text-xl font-bold mb-4">
                <i className="ri-rocket-line mr-2" aria-hidden="true"></i>
                {t('developers.quickStart.title') || 'Quick Start'}
              </h4>
              <p className="text-violet-100 mb-6">
                {t('developers.quickStart.description') || 'Get started with our APIs in minutes'}
              </p>
              <div className="flex gap-4">
                <button
                  onClick={handleViewDocs}
                  className="bg-white text-violet-600 px-6 py-3 rounded-xl font-medium hover:bg-gray-100 transition-colors duration-200 whitespace-nowrap cursor-pointer focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-violet-500"
                >
                  <i className="ri-book-open-line mr-2" aria-hidden="true"></i>
                  {t('developers.quickStart.viewDocs') || 'View Documentation'}
                </button>
                <button
                  onClick={handleDownloadSDK}
                  className="border border-white/30 text-white px-6 py-3 rounded-xl font-medium hover:bg-white/10 transition-colors duration-200 whitespace-nowrap cursor-pointer focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-violet-500"
                >
                  <i className="ri-download-line mr-2" aria-hidden="true"></i>
                  {t('developers.quickStart.downloadSDK') || 'Download SDK'}
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* 统计数据 */}
        <div className="bg-white border border-gray-200 rounded-3xl p-12 shadow-xl">
          <div className="text-center mb-12">
            <h3 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-violet-800 bg-clip-text text-transparent mb-6">
              {t('developers.trustedByDevelopers.title') || 'Trusted by Developers'}
            </h3>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto">
              {t('developers.trustedByDevelopers.description') || 'Join thousands of developers building the future of payments'}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-6 bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl hover:shadow-lg transition-all duration-300">
                <div className="text-4xl font-bold bg-gradient-to-r from-violet-600 to-blue-600 bg-clip-text text-transparent mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
