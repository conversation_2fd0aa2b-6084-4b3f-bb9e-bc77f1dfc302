'use client';

import { useState } from 'react';
import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import { useLanguage } from '@/contexts/LanguageContext';

export default function Register() {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: '',
    company: '',
    agreeTerms: false
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Register form submitted:', formData);
  };

  const features = [
    {
      icon: 'ri-shield-check-line',
      title: t('auth.register.features.bankGrade.title'),
      description: t('auth.register.features.bankGrade.description'),
      color: 'from-blue-500 to-blue-600'
    },
    {
      icon: 'ri-global-line',
      title: t('auth.register.features.globalSupport.title'),
      description: t('auth.register.features.globalSupport.description'),
      color: 'from-green-500 to-green-600'
    },
    {
      icon: 'ri-customer-service-line',
      title: t('auth.register.features.professionalService.title'),
      description: t('auth.register.features.professionalService.description'),
      color: 'from-purple-500 to-purple-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
      {/* 升级的装饰性背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-[500px] h-[500px] bg-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute top-40 right-32 w-[700px] h-[700px] bg-purple-400/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute bottom-32 left-1/3 w-96 h-96 bg-indigo-400/20 rounded-full blur-3xl animate-pulse delay-2000"></div>
        <div className="absolute top-1/3 right-1/6 w-48 h-48 bg-pink-400/15 rounded-full blur-2xl animate-pulse delay-3000"></div>
        <div className="absolute bottom-1/3 left-1/6 w-72 h-72 bg-cyan-400/15 rounded-full blur-2xl animate-pulse delay-4000"></div>
      </div>
      
      {/* 升级的网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.015)_1px,transparent_1px)] bg-[size:120px_120px]"></div>
      
      <Header />
      
      <main className="py-20 relative">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
            {/* 升级的左侧表单 */}
            <div className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="relative bg-white/95 backdrop-blur-sm rounded-3xl p-12 border border-gray-100 shadow-2xl hover:shadow-3xl transition-all duration-500">
                <div className="text-center mb-12">
                  <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
                    <i className="ri-user-add-line text-white text-4xl"></i>
                  </div>
                  <h1 className="text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
                    {t('auth.register.title')}
                  </h1>
                  <p className="text-gray-600 text-xl">
                    {t('auth.register.subtitle')}
                  </p>
                </div>
                
                <form onSubmit={handleSubmit} className="space-y-8">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        <i className="ri-user-line mr-3 text-blue-500 text-lg"></i>
                        {t('auth.register.firstName')}
                      </label>
                      <input
                        type="text"
                        value={formData.firstName}
                        onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                        className="w-full px-6 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                        placeholder={t('auth.register.firstName')}
                        required
                      />
                    </div>
                    
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        <i className="ri-user-line mr-3 text-purple-500 text-lg"></i>
                        姓氏
                      </label>
                      <input
                        type="text"
                        value={formData.lastName}
                        onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                        className="w-full px-6 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                        placeholder="请输入您的姓氏"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      <i className="ri-mail-line mr-3 text-blue-500 text-lg"></i>
                      邮箱地址
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => setFormData({...formData, email: e.target.value})}
                      className="w-full px-6 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                      placeholder="请输入您的邮箱地址"
                      required
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      <i className="ri-building-line mr-3 text-green-500 text-lg"></i>
                      公司名称 (可选)
                    </label>
                    <input
                      type="text"
                      value={formData.company}
                      onChange={(e) => setFormData({...formData, company: e.target.value})}
                      className="w-full px-6 py-4 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                      placeholder="请输入您的公司名称"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      <i className="ri-lock-line mr-3 text-purple-500 text-lg"></i>
                      密码
                    </label>
                    <div className="relative">
                      <input
                        type={showPassword ? 'text' : 'password'}
                        value={formData.password}
                        onChange={(e) => setFormData({...formData, password: e.target.value})}
                        className="w-full px-6 py-4 pr-16 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                        placeholder="请输入密码"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowPassword(!showPassword)}
                        className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-300"
                      >
                        <i className={`${showPassword ? 'ri-eye-off-line' : 'ri-eye-line'} text-xl`}></i>
                      </button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      <i className="ri-lock-line mr-3 text-orange-500 text-lg"></i>
                      确认密码
                    </label>
                    <div className="relative">
                      <input
                        type={showConfirmPassword ? 'text' : 'password'}
                        value={formData.confirmPassword}
                        onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
                        className="w-full px-6 py-4 pr-16 border border-gray-200 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 bg-white/90 backdrop-blur-sm text-lg"
                        placeholder="请再次输入密码"
                        required
                      />
                      <button
                        type="button"
                        onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                        className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-300"
                      >
                        <i className={`${showConfirmPassword ? 'ri-eye-off-line' : 'ri-eye-line'} text-xl`}></i>
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="agreeTerms"
                      checked={formData.agreeTerms}
                      onChange={(e) => setFormData({...formData, agreeTerms: e.target.checked})}
                      className="w-5 h-5 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                      required
                    />
                    <label htmlFor="agreeTerms" className="ml-3 text-sm text-gray-600">
                      我同意{' '}
                      <Link href="/terms" className="text-blue-600 hover:text-blue-800 transition-colors duration-300 font-medium">
                        服务条款
                      </Link>
                      {' '}和{' '}
                      <Link href="/privacy" className="text-blue-600 hover:text-blue-800 transition-colors duration-300 font-medium">
                        隐私政策
                      </Link>
                    </label>
                  </div>
                  
                  <button
                    type="submit"
                    className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-5 px-8 rounded-2xl font-semibold text-lg shadow-2xl hover:shadow-3xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap"
                  >
                    <i className="ri-user-add-line mr-3"></i>
                    创建账户
                  </button>
                </form>
                
                <div className="mt-10 text-center">
                  <p className="text-gray-600 text-lg">
                    已有账户？{' '}
                    <Link href="/login" className="text-blue-600 hover:text-blue-800 transition-colors duration-300 font-medium">
                      立即登录
                    </Link>
                  </p>
                </div>
              </div>
            </div>
            
            {/* 升级的右侧特性展示 */}
            <div className="space-y-12">
              <div className="text-center">
                <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-8 py-4 rounded-full text-sm font-medium mb-10 shadow-2xl">
                  <i className="ri-star-line mr-3 text-lg"></i>
                  选择我们的理由
                </div>
                <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-10">
                  加入数千家企业
                </h2>
                <p className="text-2xl text-gray-600 max-w-2xl mx-auto leading-relaxed">
                  全球领先的加密货币支付平台，为您的业务提供安全、快速、可靠的解决方案
                </p>
              </div>
              
              <div className="space-y-8">
                {features.map((feature, index) => (
                  <div key={index} className="group relative">
                    <div className="absolute inset-0 bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-3xl blur-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                    <div className="relative flex items-center p-10 bg-white/95 backdrop-blur-sm rounded-3xl border border-gray-100 shadow-2xl hover:shadow-3xl transition-all duration-500 hover:-translate-y-1">
                      <div className={`w-20 h-20 bg-gradient-to-r ${feature.color} rounded-3xl flex items-center justify-center mr-8 shadow-2xl group-hover:scale-110 transition-transform duration-300`}>
                        <i className={`${feature.icon} text-white text-3xl`}></i>
                      </div>
                      <div>
                        <h3 className="text-2xl font-bold text-gray-900 mb-3">{feature.title}</h3>
                        <p className="text-gray-600 text-lg">{feature.description}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-3xl p-12 border border-blue-100 shadow-2xl">
                <div className="text-center">
                  <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-8 shadow-2xl">
                    <i className="ri-gift-line text-white text-4xl"></i>
                  </div>
                  <h3 className="text-4xl font-bold text-gray-900 mb-6">
                    立即开始免费试用
                  </h3>
                  <p className="text-gray-600 mb-10 text-xl">
                    无需信用卡，30天免费试用期
                  </p>
                  <div className="grid grid-cols-3 gap-8 text-center">
                    <div className="p-6 bg-white/80 rounded-2xl shadow-lg">
                      <div className="text-4xl font-bold text-blue-600 mb-3">5分钟</div>
                      <div className="text-gray-600">快速设置</div>
                    </div>
                    <div className="p-6 bg-white/80 rounded-2xl shadow-lg">
                      <div className="text-4xl font-bold text-green-600 mb-3">0费用</div>
                      <div className="text-gray-600">免费试用</div>
                    </div>
                    <div className="p-6 bg-white/80 rounded-2xl shadow-lg">
                      <div className="text-4xl font-bold text-purple-600 mb-3">24/7</div>
                      <div className="text-gray-600">技术支持</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}