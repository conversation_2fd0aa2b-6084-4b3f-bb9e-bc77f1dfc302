
'use client';

import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

export default function SecurityStandards() {
  const { t } = useLanguage();
  const [activeStandard, setActiveStandard] = useState(0);

  const standards = [
    {
      name: t('compliance.securityStandards.standards.pciDss.name'),
      description: t('compliance.securityStandards.standards.pciDss.description'),
      icon: 'ri-shield-check-line',
      features: [
        t('compliance.securityStandards.standards.pciDss.features.encryption'),
        t('compliance.securityStandards.standards.pciDss.features.storage'),
        t('compliance.securityStandards.standards.pciDss.features.access'),
        t('compliance.securityStandards.standards.pciDss.features.audits')
      ],
      status: t('compliance.securityStandards.status.certified'),
      color: 'from-blue-500 to-blue-600',
      image: 'https://readdy.ai/api/search-image?query=Cybersecurity%20professionals%20monitoring%20network%20security%20dashboard%20with%20multiple%20screens%20showing%20encryption%20protocols%20and%20security%20metrics%2C%20modern%20security%20operations%20center%20with%20clean%20white%20background%2C%20professional%20technology%20environment&width=400&height=200&seq=pci-security&orientation=landscape'
    },
    {
      name: t('compliance.securityStandards.standards.iso27001.name'),
      description: t('compliance.securityStandards.standards.iso27001.description'),
      icon: 'ri-file-shield-line',
      features: [
        t('compliance.securityStandards.standards.iso27001.features.risk'),
        t('compliance.securityStandards.standards.iso27001.features.policies'),
        t('compliance.securityStandards.standards.iso27001.features.monitoring'),
        t('compliance.securityStandards.standards.iso27001.features.response')
      ],
      status: t('compliance.securityStandards.status.certified'),
      color: 'from-green-500 to-green-600',
      image: 'https://readdy.ai/api/search-image?query=Professional%20business%20team%20reviewing%20ISO%20certification%20documents%20in%20modern%20office%20conference%20room%2C%20quality%20management%20system%20documentation%20on%20table%2C%20clean%20corporate%20environment%20with%20natural%20lighting&width=400&height=200&seq=iso-cert&orientation=landscape'
    },
    {
      name: t('compliance.securityStandards.standards.soc2.name'),
      description: t('compliance.securityStandards.standards.soc2.description'),
      icon: 'ri-security-scan-line',
      features: [
        t('compliance.securityStandards.standards.soc2.features.security'),
        t('compliance.securityStandards.standards.soc2.features.availability'),
        t('compliance.securityStandards.standards.soc2.features.confidentiality'),
        t('compliance.securityStandards.standards.soc2.features.privacy')
      ],
      status: t('compliance.securityStandards.status.certified'),
      color: 'from-purple-500 to-purple-600',
      image: 'https://readdy.ai/api/search-image?query=IT%20auditor%20reviewing%20system%20controls%20and%20security%20compliance%20reports%20on%20modern%20workstation%2C%20professional%20audit%20documentation%20and%20compliance%20frameworks%2C%20clean%20office%20environment%20with%20white%20background&width=400&height=200&seq=soc-audit&orientation=landscape'
    },
    {
      name: t('compliance.securityStandards.standards.amlKyc.name'),
      description: t('compliance.securityStandards.standards.amlKyc.description'),
      icon: 'ri-user-search-line',
      features: [
        t('compliance.securityStandards.standards.amlKyc.features.verification'),
        t('compliance.securityStandards.standards.amlKyc.features.monitoring'),
        t('compliance.securityStandards.standards.amlKyc.features.assessment'),
        t('compliance.securityStandards.standards.amlKyc.features.reporting')
      ],
      status: t('compliance.securityStandards.status.implemented'),
      color: 'from-orange-500 to-orange-600',
      image: 'https://readdy.ai/api/search-image?query=Financial%20compliance%20officer%20conducting%20customer%20verification%20process%20using%20digital%20identity%20verification%20tools%2C%20modern%20compliance%20workstation%20with%20clean%20professional%20setting%2C%20regulatory%20compliance%20technology&width=400&height=200&seq=aml-kyc&orientation=landscape'
    }
  ];

  const securityMeasures = [
    {
      title: t('compliance.securityStandards.measures.encryption.title'),
      description: t('compliance.securityStandards.measures.encryption.description'),
      icon: 'ri-lock-line',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: t('compliance.securityStandards.measures.access.title'),
      description: t('compliance.securityStandards.measures.access.description'),
      icon: 'ri-shield-keyhole-line',
      color: 'from-green-500 to-green-600'
    },
    {
      title: t('compliance.securityStandards.measures.monitoring.title'),
      description: t('compliance.securityStandards.measures.monitoring.description'),
      icon: 'ri-eye-line',
      color: 'from-purple-500 to-purple-600'
    }
  ];

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-80 h-80 bg-blue-100/40 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-100/40 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>
      
      {/* 网格背景 */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(59,130,246,0.08)_1px,transparent_1px),linear-gradient(90deg,rgba(59,130,246,0.08)_1px,transparent_1px)] bg-[size:50px_50px]"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-security-scan-line mr-2"></i>
            {t('compliance.securityStandards.tagline')}
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8">
            {t('compliance.securityStandards.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('compliance.securityStandards.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-20">
          {standards.map((standard, index) => (
            <div key={index} className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative bg-white border border-gray-200 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1">
                
                {/* 认证图片 */}
                <div className="relative mb-8 overflow-hidden rounded-2xl">
                  <img 
                    src={standard.image}
                    alt={`${standard.name} security certification`}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center justify-between">
                      <div className={`w-12 h-12 bg-gradient-to-r ${standard.color} rounded-xl flex items-center justify-center`}>
                        <i className={`${standard.icon} text-white text-xl`}></i>
                      </div>
                      <span className="bg-green-500/90 text-white text-sm px-3 py-1 rounded-full backdrop-blur-sm">
                        {standard.status}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{standard.name}</h3>
                  <p className="text-gray-600">{standard.description}</p>
                </div>
                
                <div className="grid grid-cols-2 gap-4 mb-8">
                  {standard.features.map((feature, idx) => (
                    <div key={idx} className="flex items-center text-sm text-gray-600 bg-gray-50 rounded-lg p-3">
                      <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                      {feature}
                    </div>
                  ))}
                </div>
                
                <button className="w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white py-3 px-6 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-200 font-medium cursor-pointer">
                  <i className="ri-download-line mr-2"></i>
                  {t('compliance.securityStandards.downloadCertificate')}
                </button>
              </div>
            </div>
          ))}
        </div>
        
        <div className="bg-white border border-gray-200 rounded-3xl p-12 shadow-2xl">
          <div className="text-center mb-12">
            <h3 className="text-4xl font-bold bg-gradient-to-r from-gray-900 to-blue-800 bg-clip-text text-transparent mb-6">
              {t('compliance.securityStandards.multiLayered.title')}
            </h3>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
              {t('compliance.securityStandards.multiLayered.description')}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {securityMeasures.map((measure, index) => (
              <div key={index} className="group text-center p-8 bg-gray-50 rounded-2xl border border-gray-200 hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                <div className={`w-20 h-20 bg-gradient-to-r ${measure.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  <i className={`${measure.icon} text-white text-3xl`}></i>
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-4">{measure.title}</h4>
                <p className="text-gray-600 leading-relaxed">
                  {measure.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
