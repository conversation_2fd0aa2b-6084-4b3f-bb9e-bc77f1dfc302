
'use client';

import { useState } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

export default function LicensesSection() {
  const { t } = useLanguage();
  const [selectedLicense, setSelectedLicense] = useState(0);

  const licenses = [
    {
      authority: 'FinCEN (US Treasury)',
      license: 'Money Services Business',
      number: 'MSB-31000185926161',
      jurisdiction: 'United States',
      status: 'Active',
      expiry: '2026-12-31',
      description: 'Authorized to provide money transmission services across the United States',
      icon: 'ri-government-line',
      color: 'from-blue-500 to-blue-600',
      image: 'https://images.unsplash.com/photo-*************-d10d557cf95f?w=400&h=200&fit=crop&crop=center'
    },
    {
      authority: 'MAS (Singapore)',
      license: 'Digital Payment Token Services',
      number: 'DPT-2023-001-SG',
      jurisdiction: 'Singapore',
      status: 'Active',
      expiry: '2026-06-30',
      description: 'Licensed to provide digital asset services in Singapore',
      icon: 'ri-bank-line',
      color: 'from-green-500 to-green-600',
      image: 'https://images.unsplash.com/photo-*************-c627a92ad1ab?w=400&h=200&fit=crop&crop=center'
    },
    {
      authority: 'FCA (United Kingdom)',
      license: 'Electronic Money Institution',
      number: 'EMI-900123-UK',
      jurisdiction: 'United Kingdom',
      status: 'Active',
      expiry: '2025-09-15',
      description: 'Authorized to provide payment services in the UK and EU',
      icon: 'ri-building-line',
      color: 'from-purple-500 to-purple-600',
      image: 'https://images.unsplash.com/photo-*************-8130f3a36994?w=400&h=200&fit=crop&crop=center'
    },
    {
      authority: 'AUSTRAC (Australia)',
      license: 'Digital Currency Exchange',
      number: 'DCE-100456-AU',
      jurisdiction: 'Australia',
      status: 'Active',
      expiry: '2026-03-20',
      description: 'Licensed to provide digital currency exchange services',
      icon: 'ri-shield-star-line',
      color: 'from-orange-500 to-orange-600',
      image: 'https://images.unsplash.com/photo-1521737604893-d14cc237f11d?w=400&h=200&fit=crop&crop=center'
    }
  ];

  return (
    <section className="py-24 bg-white relative overflow-hidden">
      {/* 装饰性背景 */}
      <div className="absolute inset-0 bg-gradient-to-br from-gray-50 via-white to-blue-50"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        <div className="text-center mb-20">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 px-6 py-3 rounded-full text-sm font-medium mb-8">
            <i className="ri-file-shield-line mr-2"></i>
            {t('compliance.licenses.tagline')}
          </div>
          <h2 className="text-6xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-8">
            {t('compliance.licenses.title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('compliance.licenses.subtitle')}
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {licenses.map((license, index) => (
            <div key={index} className="group relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 rounded-3xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="relative bg-white border border-gray-100 rounded-3xl p-8 shadow-xl hover:shadow-2xl transition-all duration-300">
                
                {/* 许可证图片 */}
                <div className="relative mb-8 overflow-hidden rounded-2xl">
                  <img 
                    src={license.image}
                    alt={`${license.authority} headquarters`}
                    className="w-full h-48 object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center justify-between">
                      <div className={`w-12 h-12 bg-gradient-to-r ${license.color} rounded-xl flex items-center justify-center`}>
                        <i className={`${license.icon} text-white text-xl`}></i>
                      </div>
                      <span className="bg-green-500/90 text-white text-sm px-3 py-1 rounded-full backdrop-blur-sm">
                        {license.status}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="mb-6">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">{license.license}</h3>
                  <p className="text-gray-600 font-medium">{license.authority}</p>
                </div>
                
                <div className="bg-gradient-to-r from-gray-50 to-blue-50 rounded-xl p-6 mb-6 space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">{t('compliance.licenses.licenseNumber')}</span>
                    <span className="text-sm font-mono text-gray-900">{license.number}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">{t('compliance.licenses.jurisdiction')}</span>
                    <span className="text-sm font-medium text-gray-900">{license.jurisdiction}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-500">{t('compliance.licenses.validUntil')}</span>
                    <span className="text-sm font-medium text-gray-900">{license.expiry}</span>
                  </div>
                </div>
                
                <p className="text-gray-600 text-sm leading-relaxed mb-8">
                  {license.description}
                </p>
                
                <div className="flex gap-4">
                  <button className="flex-1 bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-6 rounded-xl hover:from-blue-600 hover:to-blue-700 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
                    <i className="ri-download-line mr-2"></i>
                    {t('compliance.licenses.downloadCertificate')}
                  </button>
                  <button className="flex-1 border border-gray-200 text-gray-700 py-3 px-6 rounded-xl hover:bg-gray-50 transition-all duration-200 font-medium whitespace-nowrap cursor-pointer">
                    <i className="ri-external-link-line mr-2"></i>
                    {t('compliance.licenses.verifyLicense')}
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
