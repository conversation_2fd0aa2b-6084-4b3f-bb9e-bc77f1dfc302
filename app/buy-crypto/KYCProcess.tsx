
'use client';

import { useState } from 'react';

export default function KYCProcess() {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: '账户注册',
      description: '创建您的账户，提供基本信息',
      icon: 'ri-user-add-line',
      color: 'from-blue-500 to-blue-600',
      duration: '2分钟',
      requirements: ['有效邮箱地址', '强密码设置', '手机号验证', '同意服务条款'],
      status: 'completed'
    },
    {
      title: '身份验证',
      description: '上传身份证件进行验证',
      icon: 'ri-shield-user-line',
      color: 'from-green-500 to-green-600',
      duration: '5分钟',
      requirements: ['政府颁发的身份证件', '清晰的照片', '个人信息核实', '地址证明'],
      status: 'current'
    },
    {
      title: '资金来源',
      description: '说明您的资金来源',
      icon: 'ri-file-shield-line',
      color: 'from-purple-500 to-purple-600',
      duration: '3分钟',
      requirements: ['收入证明', '银行对账单', '资金来源说明', '合规性确认'],
      status: 'pending'
    },
    {
      title: '账户激活',
      description: '完成验证，开始交易',
      icon: 'ri-check-double-line',
      color: 'from-orange-500 to-orange-600',
      duration: '即时',
      requirements: ['审核通过', '账户激活', '开始交易', '享受服务'],
      status: 'pending'
    }
  ];

  const kycLevels = [
    {
      level: 'Level 1',
      limits: { daily: '¥5,000', monthly: '¥50,000' },
      requirements: ['基本信息', '邮箱验证', '手机验证'],
      features: ['基础交易', '客服支持', '基本安全'],
      color: 'from-blue-500 to-blue-600'
    },
    {
      level: 'Level 2',
      limits: { daily: '¥50,000', monthly: '¥500,000' },
      requirements: ['身份证件', '地址证明', '人脸识别'],
      features: ['高级交易', '优先客服', '高级安全', '批量交易'],
      color: 'from-green-500 to-green-600'
    },
    {
      level: 'Level 3',
      limits: { daily: '¥500,000', monthly: '¥5,000,000' },
      requirements: ['资金证明', '银行对账单', '风险评估'],
      features: ['机构服务', '专属客服', '定制方案', '合规报告'],
      color: 'from-purple-500 to-purple-600'
    }
  ];

  return (
    <section className="py-24 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-white text-blue-600 px-6 py-3 rounded-full text-sm font-medium mb-8 border border-blue-200 shadow-sm">
            <i className="ri-shield-user-line mr-2"></i>
            KYC身份验证
          </div>
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            快速完成
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              身份验证
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            简单快捷的KYC流程，确保合规性的同时提升您的交易限额
          </p>
        </div>
        
        <div className="mb-16">
          <div className="flex justify-center mb-12">
            <div className="flex items-center space-x-8">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div className={`relative cursor-pointer transition-all duration-300 ${
                    currentStep === index ? 'scale-110' : 'hover:scale-105'
                  }`} onClick={() => setCurrentStep(index)}>
                    <div className={`w-16 h-16 rounded-2xl flex items-center justify-center shadow-lg ${
                      step.status === 'completed' ? 'bg-gradient-to-r from-green-500 to-green-600' :
                      step.status === 'current' ? 'bg-gradient-to-r from-blue-500 to-blue-600' :
                      'bg-gradient-to-r from-gray-400 to-gray-500'
                    }`}>
                      <i className={`${step.icon} text-white text-2xl`}></i>
                    </div>
                    {step.status === 'completed' && (
                      <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                        <i className="ri-check-line text-white text-sm"></i>
                      </div>
                    )}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={`w-16 h-1 mx-4 rounded-full ${
                      index < currentStep ? 'bg-green-500' : 'bg-gray-300'
                    }`}></div>
                  )}
                </div>
              ))}
            </div>
          </div>
          
          <div className="bg-white rounded-3xl p-10 border border-gray-200 shadow-xl">
            <div className="text-center mb-8">
              <div className={`w-24 h-24 bg-gradient-to-r ${steps[currentStep].color} rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg`}>
                <i className={`${steps[currentStep].icon} text-white text-4xl`}></i>
              </div>
              <h3 className="text-3xl font-bold text-gray-900 mb-2">
                {steps[currentStep].title}
              </h3>
              <p className="text-gray-600 text-lg mb-4">
                {steps[currentStep].description}
              </p>
              <div className="inline-flex items-center bg-blue-50 text-blue-600 px-4 py-2 rounded-full text-sm font-medium border border-blue-200">
                <i className="ri-time-line mr-2"></i>
                预计时间: {steps[currentStep].duration}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {steps[currentStep].requirements.map((requirement, index) => (
                <div key={index} className="bg-gray-50 rounded-xl p-6 border border-gray-200">
                  <div className="text-center">
                    <i className="ri-check-line text-green-500 text-2xl mb-3"></i>
                    <div className="text-gray-900 text-sm font-medium">{requirement}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-3xl p-10 border border-gray-200 shadow-xl">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              KYC验证等级
            </h3>
            <p className="text-gray-600 text-lg">
              不同的验证等级对应不同的交易限额和服务权限
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {kycLevels.map((level, index) => (
              <div key={index} className="group relative">
                <div className="relative bg-gray-50 rounded-2xl p-8 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="text-center mb-6">
                    <div className={`w-16 h-16 bg-gradient-to-r ${level.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                      <span className="text-white text-lg font-bold">{index + 1}</span>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">{level.level}</h4>
                  </div>
                  
                  <div className="space-y-4 mb-6">
                    <div className="bg-white rounded-xl p-4 border border-gray-200">
                      <div className="text-sm text-gray-600 mb-2">交易限额</div>
                      <div className="text-gray-900 font-medium">
                        日限额: {level.limits.daily}
                      </div>
                      <div className="text-gray-900 font-medium">
                        月限额: {level.limits.monthly}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-sm text-gray-600 mb-3">验证要求</div>
                      <div className="space-y-2">
                        {level.requirements.map((req, idx) => (
                          <div key={idx} className="flex items-center text-sm text-gray-700">
                            <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                            {req}
                          </div>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-sm text-gray-600 mb-3">服务特权</div>
                      <div className="space-y-2">
                        {level.features.map((feature, idx) => (
                          <div key={idx} className="flex items-center text-sm text-gray-700">
                            <i className="ri-star-line text-yellow-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                            {feature}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  <button className={`w-full bg-gradient-to-r ${level.color} text-white py-3 px-6 rounded-xl hover:shadow-lg transition-all duration-200 font-medium whitespace-nowrap cursor-pointer`}>
                    升级到 {level.level}
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
