
'use client';

import { useState } from 'react';

export default function LimitsSection() {
  const [activeTab, setActiveTab] = useState(0);

  const limitTiers = [
    {
      name: '基础用户',
      level: 'Level 1',
      verification: '邮箱验证',
      dailyLimit: '¥5,000',
      monthlyLimit: '¥50,000',
      features: ['基础交易', '邮箱支持', '标准处理'],
      color: 'from-blue-500 to-blue-600',
      bgColor: 'from-blue-50 to-blue-100',
      icon: 'ri-user-line',
      requirements: ['有效邮箱', '手机验证', '基本信息'],
      image: 'https://readdy.ai/api/search-image?query=basic%20user%20profile%20interface%20blue%20modern%20clean%20design%20simple%20verification%20process%20beginner%20friendly%20fintech%20dashboard%20white%20background%20professional%20minimal&width=400&height=300&seq=basic-user-upgrade-3&orientation=landscape'
    },
    {
      name: '认证用户',
      level: 'Level 2',
      verification: '身份证验证',
      dailyLimit: '¥50,000',
      monthlyLimit: '¥500,000',
      features: ['高级交易', '优先支持', '快速处理', '批量操作'],
      color: 'from-green-500 to-green-600',
      bgColor: 'from-green-50 to-green-100',
      icon: 'ri-shield-user-line',
      requirements: ['身份证件', '地址证明', '人脸识别', '银行信息'],
      image: 'https://readdy.ai/api/search-image?query=verified%20user%20identity%20verification%20interface%20green%20modern%20secure%20authentication%20process%20professional%20fintech%20dashboard%20white%20background%20clean%20minimal%20design&width=400&height=300&seq=verified-user-upgrade-3&orientation=landscape'
    },
    {
      name: 'VIP用户',
      level: 'Level 3',
      verification: '高级认证',
      dailyLimit: '¥500,000',
      monthlyLimit: '¥5,000,000',
      features: ['机构服务', '专属客服', '定制方案', '合规报告', '税务支持'],
      color: 'from-purple-500 to-purple-600',
      bgColor: 'from-purple-50 to-purple-100',
      icon: 'ri-vip-crown-line',
      requirements: ['资产证明', '收入证明', '税务文件', '合规审查'],
      image: 'https://readdy.ai/api/search-image?query=VIP%20premium%20user%20interface%20purple%20luxury%20design%20crown%20icon%20exclusive%20services%20high-end%20fintech%20dashboard%20white%20background%20elegant%20professional&width=400&height=300&seq=vip-user-upgrade-3&orientation=landscape'
    }
  ];

  const paymentLimits = [
    {
      method: '银行转账',
      icon: 'ri-bank-line',
      color: 'from-blue-500 to-blue-600',
      limits: {
        single: '¥500,000',
        daily: '¥1,000,000',
        monthly: '¥10,000,000'
      },
      processingTime: '1-3 工作日',
      fees: '0.5%'
    },
    {
      method: '信用卡',
      icon: 'ri-bank-card-line',
      color: 'from-green-500 to-green-600',
      limits: {
        single: '¥50,000',
        daily: '¥100,000',
        monthly: '¥500,000'
      },
      processingTime: '即时',
      fees: '3.5%'
    },
    {
      method: 'PayPal',
      icon: 'ri-paypal-line',
      color: 'from-purple-500 to-purple-600',
      limits: {
        single: '¥25,000',
        daily: '¥50,000',
        monthly: '¥200,000'
      },
      processingTime: '即时',
      fees: '2.9%'
    },
    {
      method: '数字钱包',
      icon: 'ri-smartphone-line',
      color: 'from-orange-500 to-orange-600',
      limits: {
        single: '¥10,000',
        daily: '¥20,000',
        monthly: '¥100,000'
      },
      processingTime: '即时',
      fees: '1.5%'
    }
  ];

  return (
    <section className="py-32 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 装饰性背景 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/4 left-1/4 w-80 h-80 bg-gradient-to-r from-blue-400/10 to-green-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative text-center mb-20">
          <div className="inline-flex items-center bg-white/80 backdrop-blur-sm text-blue-600 px-8 py-4 rounded-full text-sm font-medium mb-10 border border-blue-200 shadow-lg">
            <i className="ri-bar-chart-line mr-2"></i>
            交易限额说明
          </div>
          <h2 className="text-6xl font-bold text-gray-900 mb-8">
            了解您的
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              交易限额
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            根据您的验证等级，享受不同的交易限额和专属服务。
            <span className="text-blue-600 font-semibold">完成更高级别的验证</span>
            即可获得更高的交易限额
          </p>
        </div>

        {/* 用户等级切换 */}
        <div className="flex justify-center mb-16">
          <div className="inline-flex bg-white/80 backdrop-blur-sm rounded-2xl p-2 border border-gray-200 shadow-lg">
            {limitTiers.map((tier, index) => (
              <button
                key={index}
                onClick={() => setActiveTab(index)}
                className={`px-8 py-4 rounded-xl font-medium transition-all duration-300 whitespace-nowrap ${
                  activeTab === index
                    ? 'bg-gradient-to-r from-blue-500 to-purple-500 text-white shadow-lg'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                }`}
              >
                {tier.name}
              </button>
            ))}
          </div>
        </div>

        {/* 当前等级详情 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-12 border border-gray-200 shadow-2xl mb-16">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <div>
              <div className="mb-8">
                <div className={`w-24 h-24 bg-gradient-to-r ${limitTiers[activeTab].color} rounded-3xl flex items-center justify-center mb-6 shadow-lg`}>
                  <i className={`${limitTiers[activeTab].icon} text-white text-4xl`}></i>
                </div>
                <h3 className="text-4xl font-bold text-gray-900 mb-2">
                  {limitTiers[activeTab].name}
                </h3>
                <div className="text-lg text-gray-600 mb-6">
                  {limitTiers[activeTab].level} • {limitTiers[activeTab].verification}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className={`p-6 bg-gradient-to-r ${limitTiers[activeTab].bgColor} rounded-2xl border border-gray-200`}>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {limitTiers[activeTab].dailyLimit}
                    </div>
                    <div className="text-sm text-gray-600">日交易限额</div>
                  </div>
                </div>
                <div className={`p-6 bg-gradient-to-r ${limitTiers[activeTab].bgColor} rounded-2xl border border-gray-200`}>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-gray-900 mb-2">
                      {limitTiers[activeTab].monthlyLimit}
                    </div>
                    <div className="text-sm text-gray-600">月交易限额</div>
                  </div>
                </div>
              </div>

              <div className="space-y-6">
                <div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-4">专享服务</h4>
                  <div className="space-y-3">
                    {limitTiers[activeTab].features.map((feature, index) => (
                      <div key={index} className="flex items-center text-gray-700 p-3 bg-gray-50/80 backdrop-blur-sm rounded-xl border border-gray-200">
                        <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                          <i className="ri-check-line text-white text-sm"></i>
                        </div>
                        <span className="font-medium">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-4">验证要求</h4>
                  <div className="space-y-3">
                    {limitTiers[activeTab].requirements.map((req, index) => (
                      <div key={index} className="flex items-center text-gray-700 p-3 bg-gray-50/80 backdrop-blur-sm rounded-xl border border-gray-200">
                        <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center mr-3">
                          <i className="ri-file-text-line text-white text-sm"></i>
                        </div>
                        <span className="font-medium">{req}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
                <img 
                  src={limitTiers[activeTab].image}
                  alt={limitTiers[activeTab].name}
                  className="w-full h-full object-cover object-top"
                />
              </div>
              
              <div className="absolute -bottom-6 -right-6 bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-gray-200">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900 mb-2">
                    {limitTiers[activeTab].level}
                  </div>
                  <div className="text-sm text-gray-600 mb-4">当前等级</div>
                  <button className={`bg-gradient-to-r ${limitTiers[activeTab].color} text-white px-6 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-200 whitespace-nowrap cursor-pointer`}>
                    立即升级
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* 支付方式限额 */}
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-12 border border-gray-200 shadow-2xl">
          <div className="text-center mb-12">
            <h3 className="text-3xl font-bold text-gray-900 mb-4">
              各支付方式限额
            </h3>
            <p className="text-gray-600 text-lg">
              不同支付方式对应不同的交易限额和处理时间
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {paymentLimits.map((payment, index) => (
              <div key={index} className="group">
                <div className="bg-gray-50/80 backdrop-blur-sm rounded-2xl p-8 border border-gray-200 shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1">
                  <div className="text-center mb-6">
                    <div className={`w-20 h-20 bg-gradient-to-r ${payment.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg group-hover:shadow-xl`}>
                      <i className={`${payment.icon} text-white text-3xl`}></i>
                    </div>
                    <h4 className="text-xl font-bold text-gray-900 mb-2">{payment.method}</h4>
                    <p className="text-sm text-gray-600">{payment.processingTime}</p>
                  </div>

                  <div className="space-y-4">
                    <div className="bg-white/80 backdrop-blur-sm rounded-xl p-4 border border-gray-200">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600">单笔限额</span>
                        <span className="text-sm font-semibold text-gray-900">{payment.limits.single}</span>
                      </div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600">日限额</span>
                        <span className="text-sm font-semibold text-gray-900">{payment.limits.daily}</span>
                      </div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm text-gray-600">月限额</span>
                        <span className="text-sm font-semibold text-gray-900">{payment.limits.monthly}</span>
                      </div>
                      <div className="border-t border-gray-200 pt-3 mt-3">
                        <div className="flex justify-between items-center">
                          <span className="text-sm text-gray-600">手续费</span>
                          <span className="text-sm font-semibold text-blue-600">{payment.fees}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
