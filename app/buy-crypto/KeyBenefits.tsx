
'use client';

import { useState } from 'react';

export default function KeyBenefits() {
  const [activeBenefit, setActiveBenefit] = useState(0);

  const benefits = [
    {
      title: '极速交易',
      description: '平均处理时间仅需30秒，支持即时到账',
      icon: 'ri-flashlight-line',
      color: 'from-blue-500 to-blue-600',
      stats: { value: '30s', label: '平均处理时间' },
      features: ['即时处理', '24/7服务', '自动化系统', '实时确认'],
      image: 'https://readdy.ai/api/search-image?query=modern%20lightning%20fast%20digital%20transaction%20processing%20blue%20technological%20interface%20minimal%20clean%20white%20background%20with%20speed%20indicators%20and%20digital%20elements%20professional%20fintech%20design%20cyberpunk%20style%20&width=600&height=400&seq=buy-speed-2&orientation=landscape'
    },
    {
      title: '最低费用',
      description: '行业最低手续费，为您节省每一分钱',
      icon: 'ri-money-dollar-circle-line',
      color: 'from-green-500 to-green-600',
      stats: { value: '0.1%', label: '最低手续费' },
      features: ['透明定价', '无隐藏费用', '批量优惠', 'VIP折扣'],
      image: 'https://readdy.ai/api/search-image?query=minimal%20green%20financial%20cost%20savings%20dashboard%20with%20charts%20and%20percentage%20symbols%20clean%20professional%20design%20money%20saving%20concept%20modern%20fintech%20interface%20digital%20banking%20style%20white%20background%20&width=600&height=400&seq=buy-cost-2&orientation=landscape'
    },
    {
      title: '银行级安全',
      description: '多重加密保护，确保您的资金安全',
      icon: 'ri-shield-check-line',
      color: 'from-purple-500 to-purple-600',
      stats: { value: '99.9%', label: '安全保障' },
      features: ['多重签名', '冷钱包存储', '实时监控', '保险保障'],
      image: 'https://readdy.ai/api/search-image?query=sophisticated%20purple%20security%20shield%20with%20digital%20lock%20mechanisms%20cybersecurity%20interface%20with%20protection%20layers%20modern%20fintech%20security%20system%20clean%20professional%20white%20background%20blockchain%20style%20&width=600&height=400&seq=buy-security-2&orientation=landscape'
    },
    {
      title: '全球支持',
      description: '覆盖全球60+国家，支持多种货币',
      icon: 'ri-global-line',
      color: 'from-orange-500 to-orange-600',
      stats: { value: '60+', label: '支持国家' },
      features: ['多币种支持', '本地化服务', '合规运营', '24/7客服'],
      image: 'https://readdy.ai/api/search-image?query=global%20world%20map%20with%20orange%20connection%20lines%20international%20network%20coverage%20modern%20fintech%20global%20services%20clean%20professional%20design%20with%20currency%20symbols%20digital%20banking%20worldwide%20white%20background%20&width=600&height=400&seq=buy-global-2&orientation=landscape'
    }
  ];

  return (
    <section className="py-24 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <div className="inline-flex items-center bg-gradient-to-r from-blue-50 to-purple-50 text-blue-600 px-6 py-3 rounded-full text-sm font-medium mb-8 border border-blue-200">
            <i className="ri-star-line mr-2"></i>
            核心优势
          </div>
          <h2 className="text-5xl font-bold text-gray-900 mb-6">
            为什么选择
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              我们的平台
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们致力于为用户提供最优质的加密货币购买体验
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {benefits.map((benefit, index) => (
            <div 
              key={index}
              className={`group relative cursor-pointer transition-all duration-300 ${
                activeBenefit === index ? 'scale-105' : 'hover:scale-102'
              }`}
              onClick={() => setActiveBenefit(index)}
            >
              <div className={`relative bg-white border rounded-2xl p-8 shadow-lg transition-all duration-300 hover:shadow-xl ${
                activeBenefit === index ? 'border-blue-300 ring-2 ring-blue-100' : 'border-gray-200 hover:border-gray-300'
              }`}>
                <div className="text-center mb-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${benefit.color} rounded-2xl flex items-center justify-center mx-auto mb-4 shadow-lg`}>
                    <i className={`${benefit.icon} text-white text-3xl`}></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">{benefit.title}</h3>
                  <p className="text-gray-600 text-sm">{benefit.description}</p>
                </div>
                
                <div className="text-center p-4 bg-gray-50 rounded-xl border border-gray-200">
                  <div className="text-3xl font-bold text-gray-900 mb-1">
                    {benefit.stats.value}
                  </div>
                  <div className="text-sm text-gray-600">{benefit.stats.label}</div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="bg-white rounded-3xl p-10 border border-gray-200 shadow-xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-4xl font-bold text-gray-900 mb-6">
                {benefits[activeBenefit].title}
              </h3>
              <p className="text-gray-600 text-lg mb-8 leading-relaxed">
                {benefits[activeBenefit].description}
              </p>
              
              <div className="space-y-6">
                <h4 className="text-2xl font-semibold text-gray-900 mb-4">核心特性</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {benefits[activeBenefit].features.map((feature, index) => (
                    <div key={index} className="flex items-center text-gray-700 p-3 bg-gray-50 rounded-xl border border-gray-200">
                      <i className="ri-check-line text-green-500 mr-3 w-5 h-5 flex items-center justify-center"></i>
                      {feature}
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl border border-blue-200">
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  {benefits[activeBenefit].stats.value}
                </div>
                <div className="text-lg text-gray-600">
                  {benefits[activeBenefit].stats.label}
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-[3/2] rounded-2xl overflow-hidden shadow-2xl">
                <img 
                  src={benefits[activeBenefit].image}
                  alt={benefits[activeBenefit].title}
                  className="w-full h-full object-cover object-top"
                />
              </div>
              
              <div className="absolute -top-6 -right-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-200 shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900 mb-1">专业</div>
                  <div className="text-sm text-gray-600">服务</div>
                </div>
              </div>
              
              <div className="absolute -bottom-6 -left-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 border border-green-200 shadow-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-900 mb-1">安全</div>
                  <div className="text-sm text-gray-600">保障</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
