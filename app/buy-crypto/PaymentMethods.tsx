
'use client';

import { useState } from 'react';

export default function PaymentMethods() {
  const [activeMethod, setActiveMethod] = useState(0);

  const paymentMethods = [
    {
      name: '银行转账',
      description: '通过银行转账购买，安全可靠，费用最低',
      processingTime: '1-3 个工作日',
      fees: '0.5%',
      icon: 'ri-bank-line',
      color: 'from-blue-500 to-blue-600',
      features: ['最低费用', '大额交易', '银行级安全', '支持ACH'],
      limits: { min: '¥100', max: '¥500,000' },
      image: 'https://readdy.ai/api/search-image?query=modern%20bank%20transfer%20interface%20clean%20blue%20financial%20dashboard%20with%20security%20icons%20money%20transfer%20system%20professional%20banking%20design%20white%20background%20minimalist%20fintech%20style&width=600&height=400&seq=bank-transfer-upgrade-3&orientation=landscape'
    },
    {
      name: '借记卡/信用卡',
      description: '即时购买，信用卡或借记卡支付，24/7可用',
      processingTime: '即时到账',
      fees: '3.5%',
      icon: 'ri-bank-card-line',
      color: 'from-green-500 to-green-600',
      features: ['即时到账', '便捷支付', '全球支持', '24/7可用'],
      limits: { min: '¥50', max: '¥50,000' },
      image: 'https://readdy.ai/api/search-image?query=credit%20card%20payment%20interface%20green%20modern%20financial%20design%20secure%20payment%20system%20card%20icons%20instant%20transaction%20professional%20fintech%20dashboard%20white%20background%20clean%20minimalist&width=600&height=400&seq=card-payment-upgrade-3&orientation=landscape'
    },
    {
      name: 'PayPal',
      description: '使用PayPal余额或关联账户，全球认可',
      processingTime: '即时到账',
      fees: '2.9%',
      icon: 'ri-paypal-line',
      color: 'from-purple-500 to-purple-600',
      features: ['快速支付', '买家保护', '便捷退款', '全球认可'],
      limits: { min: '¥20', max: '¥25,000' },
      image: 'https://readdy.ai/api/search-image?query=PayPal%20payment%20interface%20purple%20modern%20digital%20wallet%20design%20secure%20online%20payment%20system%20global%20payment%20icons%20professional%20fintech%20dashboard%20white%20background%20clean%20minimalist&width=600&height=400&seq=paypal-upgrade-3&orientation=landscape'
    },
    {
      name: '数字钱包',
      description: '使用Apple Pay、Google Pay等移动支付',
      processingTime: '即时到账',
      fees: '1.5%',
      icon: 'ri-smartphone-line',
      color: 'from-orange-500 to-orange-600',
      features: ['生物识别', '快速支付', '移动优先', '安全便捷'],
      limits: { min: '¥10', max: '¥10,000' },
      image: 'https://readdy.ai/api/search-image?query=digital%20wallet%20mobile%20payment%20interface%20orange%20modern%20smartphone%20with%20payment%20icons%20Apple%20Pay%20Google%20Pay%20biometric%20security%20professional%20fintech%20design%20white%20background%20clean%20minimalist&width=600&height=400&seq=wallet-upgrade-3&orientation=landscape'
    }
  ];

  return (
    <section className="py-32 bg-gradient-to-b from-white to-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 装饰性背景 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/3 left-1/5 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
          <div className="absolute bottom-1/3 right-1/5 w-80 h-80 bg-gradient-to-r from-green-400/10 to-orange-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        </div>

        <div className="relative text-center mb-20">
          <div className="inline-flex items-center bg-white/80 backdrop-blur-sm text-blue-600 px-8 py-4 rounded-full text-sm font-medium mb-10 border border-blue-200 shadow-lg">
            <i className="ri-secure-payment-line mr-2"></i>
            安全支付保障
          </div>
          <h2 className="text-6xl font-bold text-gray-900 mb-8">
            选择您的
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              支付方式
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            支持多种安全的支付方式，满足不同用户的需求。
            <span className="text-blue-600 font-semibold">银行级安全</span>
            保护您的每一笔交易
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {paymentMethods.map((method, index) => (
            <div 
              key={index}
              className={`group relative cursor-pointer transition-all duration-500 ${
                activeMethod === index ? 'scale-110 z-10' : 'hover:scale-105'
              }`}
              onClick={() => setActiveMethod(index)}
            >
              <div className={`relative bg-white/80 backdrop-blur-sm border rounded-3xl p-8 shadow-xl transition-all duration-300 hover:shadow-2xl ${
                activeMethod === index ? 'border-blue-300 ring-4 ring-blue-100' : 'border-gray-200 hover:border-gray-300'
              }`}>
                <div className="text-center mb-6">
                  <div className={`w-20 h-20 bg-gradient-to-r ${method.color} rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg group-hover:shadow-xl transition-all duration-300`}>
                    <i className={`${method.icon} text-white text-3xl`}></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{method.name}</h3>
                  <p className="text-gray-600 text-sm leading-relaxed">{method.description}</p>
                </div>
                
                <div className="space-y-4">
                  <div className="bg-gray-50/80 backdrop-blur-sm rounded-2xl p-4 border border-gray-200">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">处理时间</span>
                      <span className="text-sm font-semibold text-gray-900">{method.processingTime}</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm text-gray-600">手续费</span>
                      <span className="text-sm font-semibold text-gray-900">{method.fees}</span>
                    </div>
                    <div className="border-t border-gray-200 pt-3 mt-3">
                      <div className="text-xs text-gray-500 mb-2">交易限额</div>
                      <div className="flex justify-between text-xs">
                        <span className="text-gray-700">最低: {method.limits.min}</span>
                        <span className="text-gray-700">最高: {method.limits.max}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {activeMethod === index && (
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
                    <i className="ri-check-line text-white text-sm"></i>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-12 border border-gray-200 shadow-2xl">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <div>
              <div className="mb-8">
                <div className={`w-24 h-24 bg-gradient-to-r ${paymentMethods[activeMethod].color} rounded-3xl flex items-center justify-center mb-6 shadow-lg`}>
                  <i className={`${paymentMethods[activeMethod].icon} text-white text-4xl`}></i>
                </div>
                <h3 className="text-4xl font-bold text-gray-900 mb-4">
                  {paymentMethods[activeMethod].name}
                </h3>
                <p className="text-gray-600 text-lg leading-relaxed mb-8">
                  {paymentMethods[activeMethod].description}
                </p>
              </div>
              
              <div className="space-y-6">
                <h4 className="text-2xl font-semibold text-gray-900 mb-6">核心优势</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {paymentMethods[activeMethod].features.map((feature, index) => (
                    <div key={index} className="flex items-center text-gray-700 p-4 bg-gray-50/80 backdrop-blur-sm rounded-xl border border-gray-200">
                      <div className="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mr-3">
                        <i className="ri-check-line text-white text-sm"></i>
                      </div>
                      <span className="font-medium">{feature}</span>
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="mt-8 p-6 bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-2xl border border-blue-200/50">
                <div className="grid grid-cols-2 gap-6">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900 mb-2">
                      {paymentMethods[activeMethod].processingTime}
                    </div>
                    <div className="text-sm text-gray-600">处理时间</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600 mb-2">
                      {paymentMethods[activeMethod].fees}
                    </div>
                    <div className="text-sm text-gray-600">手续费</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="relative">
              <div className="aspect-[3/2] rounded-2xl overflow-hidden shadow-2xl">
                <img 
                  src={paymentMethods[activeMethod].image}
                  alt={paymentMethods[activeMethod].name}
                  className="w-full h-full object-cover object-top"
                />
              </div>
              
              {/* 快速购买表单 */}
              <div className="absolute -bottom-8 -right-8 bg-white/95 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-gray-200 max-w-sm">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">快速购买</h4>
                <div className="space-y-4">
                  <div>
                    <input
                      type="text"
                      placeholder="输入金额"
                      className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                  
                  <div className="relative">
                    <select className="w-full px-4 py-3 bg-gray-50 border border-gray-200 rounded-xl text-gray-900 appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-8">
                      <option>Bitcoin (BTC)</option>
                      <option>Ethereum (ETH)</option>
                      <option>USDT</option>
                    </select>
                    <i className="ri-arrow-down-s-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                  </div>
                  
                  <button className={`w-full bg-gradient-to-r ${paymentMethods[activeMethod].color} text-white py-3 px-6 rounded-xl hover:shadow-lg transition-all duration-200 font-medium whitespace-nowrap cursor-pointer`}>
                    <i className="ri-shopping-cart-line mr-2"></i>
                    立即购买
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
