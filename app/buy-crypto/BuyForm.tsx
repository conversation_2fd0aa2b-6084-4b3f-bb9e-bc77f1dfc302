
'use client';

import { useState, useMemo, useCallback, useEffect } from 'react';
import { useLanguage } from '@/contexts/LanguageContext';

// TypeScript interfaces for better type safety
interface Currency {
  symbol: string;
  name: string;
  logo: string;
  price: number;
  trend: 'up' | 'down' | 'stable';
}

interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  fee: number;
  icon: string;
  color: string;
}

interface FormErrors {
  amount?: string;
  currency?: string;
  paymentMethod?: string;
}

export default function BuyForm() {
  const { t } = useLanguage();
  const [currency, setCurrency] = useState('BTC');
  const [amount, setAmount] = useState('');
  const [paymentMethod, setPaymentMethod] = useState('card');
  const [showDropdown, setShowDropdown] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  // Memoized currencies data
  const currencies = useMemo<Currency[]>(() => [
    {
      symbol: 'BTC',
      name: 'Bitcoin',
      logo: 'https://readdy.ai/api/search-image?query=bitcoin%20cryptocurrency%20logo%20symbol%20golden%20orange%20coin%20digital%20currency%20icon%20clean%20minimal%20modern%20design%20white%20background%20professional&width=100&height=100&seq=btc-logo-3&orientation=squarish',
      price: 45250.32,
      trend: 'up' as const
    },
    {
      symbol: 'ETH',
      name: 'Ethereum',
      logo: 'https://readdy.ai/api/search-image?query=ethereum%20cryptocurrency%20logo%20symbol%20blue%20purple%20diamond%20crystal%20digital%20currency%20icon%20clean%20minimal%20modern%20design%20white%20background%20professional&width=100&height=100&seq=eth-logo-3&orientation=squarish',
      price: 2840.18,
      trend: 'down' as const
    },
    {
      symbol: 'USDT',
      name: 'Tether',
      logo: 'https://readdy.ai/api/search-image?query=tether%20USDT%20cryptocurrency%20logo%20symbol%20green%20stable%20coin%20digital%20currency%20icon%20clean%20minimal%20modern%20design%20white%20background%20professional&width=100&height=100&seq=usdt-logo-3&orientation=squarish',
      price: 1.00,
      trend: 'stable' as const
    },
    {
      symbol: 'BNB',
      name: 'Binance Coin',
      logo: 'https://readdy.ai/api/search-image?query=binance%20BNB%20cryptocurrency%20logo%20symbol%20yellow%20gold%20coin%20digital%20currency%20icon%20clean%20minimal%20modern%20design%20white%20background%20professional&width=100&height=100&seq=bnb-logo-3&orientation=squarish',
      price: 298.45,
      trend: 'up' as const
    },
    {
      symbol: 'ADA',
      name: 'Cardano',
      logo: 'https://readdy.ai/api/search-image?query=cardano%20ADA%20cryptocurrency%20logo%20symbol%20blue%20circular%20coin%20digital%20currency%20icon%20clean%20minimal%20modern%20design%20white%20background%20professional&width=100&height=100&seq=ada-logo-3&orientation=squarish',
      price: 0.52,
      trend: 'up' as const
    },
    {
      symbol: 'DOT',
      name: 'Polkadot',
      logo: 'https://readdy.ai/api/search-image?query=polkadot%20DOT%20cryptocurrency%20logo%20symbol%20pink%20purple%20dots%20circular%20coin%20digital%20currency%20icon%20clean%20minimal%20modern%20design%20white%20background%20professional&width=100&height=100&seq=dot-logo-3&orientation=squarish',
      price: 7.23,
      trend: 'down' as const
    }
  ], []);

  // Memoized payment methods
  const paymentMethods = useMemo<PaymentMethod[]>(() => [
    {
      id: 'card',
      name: t('buyCrypto.form.creditCard'),
      description: t('buyCrypto.form.cardDescription') || 'Instant • 3.5% fee',
      fee: 3.5,
      icon: 'ri-bank-card-line',
      color: 'from-blue-500 to-purple-500'
    },
    {
      id: 'transfer',
      name: t('buyCrypto.form.bankTransfer'),
      description: t('buyCrypto.form.transferDescription') || '1-3 business days • 0.5% fee',
      fee: 0.5,
      icon: 'ri-bank-line',
      color: 'from-green-500 to-emerald-500'
    }
  ], [t]);

  // Safe currency selection with fallback
  const selectedCurrency = useMemo(() => {
    const found = currencies.find(c => c.symbol === currency);
    return found || currencies[0]; // Fallback to first currency if not found
  }, [currencies, currency]);

  // Memoized total amount calculation with error handling
  const totalAmount = useMemo(() => {
    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      return '0.00';
    }
    const numAmount = parseFloat(amount);
    const currentPaymentMethod = paymentMethods.find(pm => pm.id === paymentMethod);
    const fee = currentPaymentMethod ? currentPaymentMethod.fee / 100 : 0;
    const subtotal = numAmount * selectedCurrency.price;
    const total = subtotal * (1 + fee);
    return total.toFixed(2);
  }, [amount, selectedCurrency.price, paymentMethod, paymentMethods]);

  // Form validation
  const validateForm = useCallback((): boolean => {
    const newErrors: FormErrors = {};

    if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
      newErrors.amount = t('buyCrypto.form.errors.invalidAmount') || 'Please enter a valid amount';
    }

    if (parseFloat(amount) > 0 && parseFloat(amount) < 0.000001) {
      newErrors.amount = t('buyCrypto.form.errors.minimumAmount') || 'Minimum amount is 0.000001';
    }

    if (!currency) {
      newErrors.currency = t('buyCrypto.form.errors.selectCurrency') || 'Please select a currency';
    }

    if (!paymentMethod) {
      newErrors.paymentMethod = t('buyCrypto.form.errors.selectPayment') || 'Please select a payment method';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [amount, currency, paymentMethod, t]);

  // Handle form submission with validation
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      console.log('Purchase order:', {
        currency,
        amount,
        paymentMethod,
        totalAmount,
        selectedCurrency: selectedCurrency.name,
        timestamp: new Date().toISOString()
      });

      // Reset form on success
      setAmount('');
      setErrors({});

      // Show success message (you can implement a toast notification here)
      alert(t('buyCrypto.form.success') || 'Purchase order submitted successfully!');

    } catch (error) {
      console.error('Purchase failed:', error);
      alert(t('buyCrypto.form.error') || 'Purchase failed. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, currency, amount, paymentMethod, totalAmount, selectedCurrency.name, t]);

  // Handle amount input with validation
  const handleAmountChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setAmount(value);

    // Clear amount error when user starts typing
    if (errors.amount) {
      setErrors(prev => ({ ...prev, amount: undefined }));
    }
  }, [errors.amount]);

  // Handle currency selection
  const handleCurrencySelect = useCallback((currencySymbol: string) => {
    setCurrency(currencySymbol);
    setShowDropdown(false);

    // Clear currency error
    if (errors.currency) {
      setErrors(prev => ({ ...prev, currency: undefined }));
    }
  }, [errors.currency]);

  // Handle payment method selection
  const handlePaymentMethodSelect = useCallback((methodId: string) => {
    setPaymentMethod(methodId);

    // Clear payment method error
    if (errors.paymentMethod) {
      setErrors(prev => ({ ...prev, paymentMethod: undefined }));
    }
  }, [errors.paymentMethod]);

  // Get trend icon with proper typing
  const getTrendIcon = useCallback((trend: Currency['trend']): string => {
    switch(trend) {
      case 'up': return 'ri-arrow-up-line text-green-500';
      case 'down': return 'ri-arrow-down-line text-red-500';
      case 'stable': return 'ri-subtract-line text-gray-500';
      default: return 'ri-subtract-line text-gray-500';
    }
  }, []);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (showDropdown) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [showDropdown]);

  return (
    <section className="py-32 bg-gradient-to-b from-gray-50 to-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* 装饰性背景 */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-purple-400/10 rounded-full blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-pink-400/10 to-orange-400/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative text-center mb-20">
          <div className="inline-flex items-center bg-white/80 backdrop-blur-sm text-blue-600 px-8 py-4 rounded-full text-sm font-medium mb-10 border border-blue-200 shadow-lg">
            <i className="ri-shopping-cart-line mr-2"></i>
            {t('buyCrypto.form.smartSystem')}
          </div>
          <h2 className="text-6xl font-bold text-gray-900 mb-8">
            {t('buyCrypto.form.journeyTitle')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {t('buyCrypto.form.journeyDescription')}
          </p>
        </div>

        <div className="max-w-xl mx-auto">
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-10 shadow-2xl border border-gray-200/50">
            <div className="text-center mb-10">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-3xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                <i className="ri-coin-line text-white text-3xl"></i>
              </div>
              <h3 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-3">
                {t('buyCrypto.form.purchaseTitle')}
              </h3>
              <p className="text-gray-600 text-lg">{t('buyCrypto.form.purchaseDescription')}</p>
            </div>
            
            <form onSubmit={handleSubmit} className="space-y-8">
              <div className="relative">
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  {t('buyCrypto.form.selectCurrency')}
                </label>
                <div className="relative">
                  <button
                    type="button"
                    onClick={() => setShowDropdown(!showDropdown)}
                    className="w-full bg-gray-50/80 backdrop-blur-sm border border-gray-200 rounded-2xl px-6 py-5 text-left hover:border-blue-300 hover:bg-white transition-all duration-300 flex items-center justify-between shadow-sm"
                  >
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-xl overflow-hidden mr-4 shadow-md">
                        <img 
                          src={selectedCurrency.logo} 
                          alt={selectedCurrency.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <div className="font-semibold text-gray-900 text-lg">{selectedCurrency.symbol}</div>
                        <div className="text-sm text-gray-600">{selectedCurrency.name}</div>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <div className="mr-3 text-right">
                        <div className="text-lg font-bold text-gray-900">${selectedCurrency.price.toFixed(2)}</div>
                        <div className="flex items-center text-sm">
                          <i className={getTrendIcon(selectedCurrency.trend) + ' mr-1'}></i>
                          {selectedCurrency.trend}
                        </div>
                      </div>
                      <i className={`ri-arrow-${showDropdown ? 'up' : 'down'}-s-line text-gray-400 text-xl`}></i>
                    </div>
                  </button>
                  
                  {showDropdown && (
                    <div className="absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-2xl shadow-2xl z-10 max-h-80 overflow-y-auto">
                      {currencies.map((curr) => (
                        <button
                          key={curr.symbol}
                          type="button"
                          onClick={() => {
                            setCurrency(curr.symbol);
                            setShowDropdown(false);
                          }}
                          className="w-full px-6 py-4 text-left hover:bg-blue-50 transition-colors duration-200 flex items-center border-b border-gray-100 last:border-b-0"
                        >
                          <div className="w-12 h-12 rounded-xl overflow-hidden mr-4 shadow-md">
                            <img 
                              src={curr.logo} 
                              alt={curr.name}
                              className="w-full h-full object-cover"
                            />
                          </div>
                          <div className="flex-1">
                            <div className="font-semibold text-gray-900 text-lg">{curr.symbol}</div>
                            <div className="text-sm text-gray-600">{curr.name}</div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-gray-900">${curr.price.toFixed(2)}</div>
                            <div className="flex items-center text-sm">
                              <i className={getTrendIcon(curr.trend) + ' mr-1'}></i>
                              {curr.trend}
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-3">
                  购买数量
                </label>
                <div className="relative">
                  <input
                    type="number"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    className="w-full bg-gray-50/80 backdrop-blur-sm border border-gray-200 rounded-2xl px-6 py-5 text-lg focus:outline-none focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 transition-all duration-300 shadow-sm"
                    placeholder="输入数量"
                    step="0.000001"
                    min="0"
                  />
                  <div className="absolute right-6 top-1/2 transform -translate-y-1/2 text-gray-500 text-lg font-semibold">
                    {currency}
                  </div>
                </div>
              </div>
              
              <div>
                <label className="block text-sm font-semibold text-gray-700 mb-4">
                  支付方式
                </label>
                <div className="space-y-4">
                  <button
                    type="button"
                    onClick={() => setPaymentMethod('card')}
                    className={`w-full p-6 rounded-2xl border-2 transition-all duration-300 flex items-center shadow-sm hover:shadow-md ${
                      paymentMethod === 'card' 
                        ? 'border-blue-500 bg-blue-50/50 ring-2 ring-blue-100' 
                        : 'border-gray-200 hover:border-gray-300 bg-white/50'
                    }`}
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                      <i className="ri-bank-card-line text-white text-xl"></i>
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-semibold text-gray-900 text-lg">银行卡支付</div>
                      <div className="text-sm text-gray-600">即时到账 • 手续费 3.5%</div>
                    </div>
                    {paymentMethod === 'card' && (
                      <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <i className="ri-check-line text-white"></i>
                      </div>
                    )}
                  </button>
                  
                  <button
                    type="button"
                    onClick={() => setPaymentMethod('transfer')}
                    className={`w-full p-6 rounded-2xl border-2 transition-all duration-300 flex items-center shadow-sm hover:shadow-md ${
                      paymentMethod === 'transfer' 
                        ? 'border-green-500 bg-green-50/50 ring-2 ring-green-100' 
                        : 'border-gray-200 hover:border-gray-300 bg-white/50'
                    }`}
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center mr-4 shadow-lg">
                      <i className="ri-bank-line text-white text-xl"></i>
                    </div>
                    <div className="flex-1 text-left">
                      <div className="font-semibold text-gray-900 text-lg">银行转账</div>
                      <div className="text-sm text-gray-600">1-3个工作日 • 手续费 0.5%</div>
                    </div>
                    {paymentMethod === 'transfer' && (
                      <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <i className="ri-check-line text-white"></i>
                      </div>
                    )}
                  </button>
                </div>
              </div>
              
              <div className="bg-gradient-to-r from-blue-50/50 to-purple-50/50 rounded-2xl p-8 space-y-4 border border-blue-200/50 shadow-sm">
                <h4 className="font-semibold text-gray-900 text-lg mb-4">订单摘要</h4>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">购买数量</span>
                  <span className="font-semibold text-gray-900">{amount || '0'} {currency}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">当前价格</span>
                  <span className="font-semibold text-gray-900">${selectedCurrency.price.toFixed(2)}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">手续费</span>
                  <span className="font-semibold text-gray-900">{paymentMethod === 'card' ? '3.5%' : '0.5%'}</span>
                </div>
                <div className="border-t border-gray-200 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-xl font-semibold text-gray-900">总计</span>
                    <span className="text-2xl font-bold text-blue-600">${totalAmount}</span>
                  </div>
                </div>
              </div>
              
              <button
                type="submit"
                className="w-full bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 hover:from-blue-700 hover:via-purple-700 hover:to-pink-700 text-white font-semibold py-5 px-8 rounded-2xl transition-all duration-300 shadow-2xl shadow-blue-500/25 hover:shadow-blue-500/40 hover:scale-105 transform whitespace-nowrap"
              >
                <i className="ri-shopping-cart-line mr-3 text-xl"></i>
                确认购买
              </button>
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}
