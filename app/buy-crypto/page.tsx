
'use client';

import { Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import LoadingFallback from '@/components/LoadingFallback';
import BuyHeroSection from './BuyHeroSection';
import PaymentMethods from './PaymentMethods';
import BuyForm from './BuyForm';
import KeyBenefits from './KeyBenefits';
import KYCProcess from './KYCProcess';
import LimitsSection from './LimitsSection';

export default function BuyCrypto() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense fallback={<LoadingFallback icon="ri-coin-line" />}>
          <BuyHeroSection />
          <PaymentMethods />
          <BuyForm />
          <KeyBenefits />
          <KYCProcess />
          <LimitsSection />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}
