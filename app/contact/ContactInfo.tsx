'use client';

export default function ContactInfo() {
  const contactMethods = [
    {
      title: '销售咨询',
      description: '了解我们的产品和服务',
      info: '<EMAIL>',
      icon: 'ri-shopping-bag-line',
      color: 'bg-blue-600'
    },
    {
      title: '技术支持',
      description: '获取技术帮助和集成支持',
      info: '<EMAIL>',
      icon: 'ri-tools-line',
      color: 'bg-green-600'
    },
    {
      title: '商务合作',
      description: '探讨合作机会',
      info: '<EMAIL>',
      icon: 'ri-handshake-line',
      color: 'bg-purple-600'
    },
    {
      title: '媒体采访',
      description: '媒体相关询问',
      info: '<EMAIL>',
      icon: 'ri-newspaper-line',
      color: 'bg-orange-600'
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            其他联系方式
          </h2>
          <p className="text-xl text-gray-600">
            选择最适合您需求的联系方式
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {contactMethods.map((method, index) => (
            <div key={index} className="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow duration-300 p-6">
              <div className="text-center">
                <div className={`w-16 h-16 ${method.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                  <i className={`${method.icon} text-white text-2xl`}></i>
                </div>
                <h3 className="text-xl font-semibold text-gray-900 mb-2">
                  {method.title}
                </h3>
                <p className="text-gray-600 text-sm mb-4">
                  {method.description}
                </p>
                <a 
                  href={`mailto:${method.info}`}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  {method.info}
                </a>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-16 bg-white rounded-xl shadow-md p-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-6">
                24/7 客户支持
              </h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <i className="ri-phone-line text-blue-600 text-xl mr-3"></i>
                  <div>
                    <div className="font-medium text-gray-900">电话支持</div>
                    <div className="text-gray-600">+****************</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <i className="ri-message-3-line text-blue-600 text-xl mr-3"></i>
                  <div>
                    <div className="font-medium text-gray-900">在线客服</div>
                    <div className="text-gray-600">24小时在线支持</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <i className="ri-whatsapp-line text-blue-600 text-xl mr-3"></i>
                  <div>
                    <div className="font-medium text-gray-900">WhatsApp</div>
                    <div className="text-gray-600">+65 8123 4567</div>
                  </div>
                </div>
                
                <div className="flex items-center">
                  <i className="ri-telegram-line text-blue-600 text-xl mr-3"></i>
                  <div>
                    <div className="font-medium text-gray-900">Telegram</div>
                    <div className="text-gray-600">@cryptopay_support</div>
                  </div>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 mb-6">
                社交媒体
              </h3>
              <div className="grid grid-cols-3 gap-4">
                <a href="#" className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <i className="ri-twitter-line text-blue-500 text-2xl mb-2"></i>
                  <span className="text-sm text-gray-600">Twitter</span>
                </a>
                
                <a href="#" className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <i className="ri-linkedin-line text-blue-700 text-2xl mb-2"></i>
                  <span className="text-sm text-gray-600">LinkedIn</span>
                </a>
                
                <a href="#" className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <i className="ri-github-line text-gray-800 text-2xl mb-2"></i>
                  <span className="text-sm text-gray-600">GitHub</span>
                </a>
                
                <a href="#" className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <i className="ri-youtube-line text-red-600 text-2xl mb-2"></i>
                  <span className="text-sm text-gray-600">YouTube</span>
                </a>
                
                <a href="#" className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <i className="ri-discord-line text-purple-600 text-2xl mb-2"></i>
                  <span className="text-sm text-gray-600">Discord</span>
                </a>
                
                <a href="#" className="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                  <i className="ri-reddit-line text-orange-600 text-2xl mb-2"></i>
                  <span className="text-sm text-gray-600">Reddit</span>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}