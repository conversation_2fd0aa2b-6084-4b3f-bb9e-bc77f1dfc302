'use client';

import { Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import ContactHero from './ContactHero';
import ContactForm from './ContactForm';
import ContactInfo from './ContactInfo';
import OfficeLocations from './OfficeLocations';

export default function Contact() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense fallback={
          <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 relative overflow-hidden">
            {/* 升级的加载动画背景 */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-20 w-96 h-96 bg-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-20 right-20 w-80 h-80 bg-purple-400/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-green-400/15 rounded-full blur-2xl animate-pulse delay-2000"></div>
            </div>
            
            {/* 升级的联系装饰 */}
            <div className="absolute inset-0 bg-[linear-gradient(rgba(0,0,0,0.015)_1px,transparent_1px),linear-gradient(90deg,rgba(0,0,0,0.015)_1px,transparent_1px)] bg-[size:100px_100px]"></div>
            
            <div className="text-center relative z-10">
              <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-2xl">
                <i className="ri-customer-service-line text-white text-3xl"></i>
              </div>
              <div className="text-gray-600 font-medium text-2xl mb-4">加载联系方式...</div>
              <div className="flex justify-center mb-6">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
              </div>
              <div className="text-sm text-gray-500">正在准备联系表单</div>
            </div>
          </div>
        }>
          <ContactHero />
          <div className="py-24 bg-gradient-to-br from-gray-50 via-white to-blue-50 relative overflow-hidden">
            {/* 升级的装饰性背景 */}
            <div className="absolute inset-0">
              <div className="absolute top-20 left-20 w-80 h-80 bg-blue-400/15 rounded-full blur-3xl animate-pulse"></div>
              <div className="absolute bottom-20 right-20 w-96 h-96 bg-purple-400/15 rounded-full blur-3xl animate-pulse delay-1000"></div>
            </div>
            
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-20">
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/15 to-purple-500/15 rounded-3xl blur-2xl opacity-50"></div>
                  <div className="relative">
                    <ContactForm />
                  </div>
                </div>
                <div className="relative">
                  <div className="absolute inset-0 bg-gradient-to-r from-purple-500/15 to-blue-500/15 rounded-3xl blur-2xl opacity-50"></div>
                  <div className="relative">
                    <ContactInfo />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <OfficeLocations />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}