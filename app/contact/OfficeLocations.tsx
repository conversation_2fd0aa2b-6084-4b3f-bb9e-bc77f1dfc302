'use client';

export default function OfficeLocations() {
  const offices = [
    {
      city: '新加坡',
      country: '新加坡',
      address: '1 Marina Bay Financial Centre, Tower 1, #12-34',
      phone: '+65 6123 4567',
      email: '<EMAIL>',
      timezone: 'GMT+8',
      hours: '周一至周五 9:00-18:00',
      isHeadquarters: true
    },
    {
      city: '纽约',
      country: '美国',
      address: '200 West Street, New York, NY 10282',
      phone: '+****************',
      email: '<EMAIL>',
      timezone: 'GMT-5',
      hours: '周一至周五 9:00-17:00',
      isHeadquarters: false
    },
    {
      city: '伦敦',
      country: '英国',
      address: '1 King William Street, London EC4N 7AF',
      phone: '+44 20 7123 4567',
      email: '<EMAIL>',
      timezone: 'GMT+0',
      hours: '周一至周五 9:00-17:00',
      isHeadquarters: false
    },
    {
      city: '迪拜',
      country: '阿联酋',
      address: 'Dubai International Financial Centre, Gate Building',
      phone: '+971 4 123 4567',
      email: '<EMAIL>',
      timezone: 'GMT+4',
      hours: '周日至周四 9:00-17:00',
      isHeadquarters: false
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            全球办公室
          </h2>
          <p className="text-xl text-gray-600">
            我们在全球主要金融中心设有办公室
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            {offices.map((office, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <div className="flex items-center mb-2">
                      <h3 className="text-xl font-semibold text-gray-900 mr-2">
                        {office.city}
                      </h3>
                      {office.isHeadquarters && (
                        <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                          总部
                        </span>
                      )}
                    </div>
                    <p className="text-gray-600">{office.country}</p>
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-500">{office.timezone}</div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-start">
                    <i className="ri-map-pin-line text-gray-400 text-lg mr-3 mt-0.5"></i>
                    <div>
                      <div className="text-sm text-gray-600">{office.address}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <i className="ri-phone-line text-gray-400 text-lg mr-3"></i>
                    <div>
                      <a href={`tel:${office.phone}`} className="text-sm text-blue-600 hover:text-blue-700">
                        {office.phone}
                      </a>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <i className="ri-mail-line text-gray-400 text-lg mr-3"></i>
                    <div>
                      <a href={`mailto:${office.email}`} className="text-sm text-blue-600 hover:text-blue-700">
                        {office.email}
                      </a>
                    </div>
                  </div>
                  
                  <div className="flex items-center">
                    <i className="ri-time-line text-gray-400 text-lg mr-3"></i>
                    <div>
                      <div className="text-sm text-gray-600">{office.hours}</div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="bg-white rounded-xl shadow-md p-6">
            <h3 className="text-xl font-semibold text-gray-900 mb-4">
              办公室地图
            </h3>
            <div className="h-96 bg-gray-100 rounded-lg">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.7891557540437!2d103.85373731475397!3d1.2834236990685444!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x31da19052c6a8d8b%3A0xd3a7c8b0b5b5b5b5!2sMarina%20Bay%20Financial%20Centre!5e0!3m2!1sen!2ssg!4v1234567890"
                width="100%"
                height="100%"
                className="rounded-lg"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                referrerPolicy="no-referrer-when-downgrade"
              ></iframe>
            </div>
            
            <div className="mt-4 text-sm text-gray-600">
              <p className="mb-2">
                <i className="ri-map-pin-line text-blue-600 mr-2"></i>
                总部位于新加坡滨海湾金融中心
              </p>
              <p>
                <i className="ri-global-line text-blue-600 mr-2"></i>
                全球 4 个办公室为您提供本地化服务
              </p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}