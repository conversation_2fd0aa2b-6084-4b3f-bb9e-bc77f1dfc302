'use client';

import Link from 'next/link';
import Button from '@/components/ui/Button';

export default function CTASection() {
  return (
    <section className="py-20 bg-blue-600 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 className="text-4xl font-bold mb-6">
          准备开始了吗？
        </h2>
        <p className="text-xl mb-8 text-blue-100 max-w-2xl mx-auto">
          立即联系我们的专业团队，为您的业务定制最适合的加密货币支付解决方案
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Link href="/contact">
            <Button size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
              <i className="ri-phone-line w-5 h-5 flex items-center justify-center mr-2"></i>
              联系销售
            </Button>
          </Link>
          <Link href="/buy-crypto">
            <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10">
              <i className="ri-shopping-cart-line w-5 h-5 flex items-center justify-center mr-2"></i>
              立即试用
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}