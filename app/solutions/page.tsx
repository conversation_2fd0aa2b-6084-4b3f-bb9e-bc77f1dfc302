'use client';

import { Suspense } from 'react';
import Header from '@/components/Header';
import Footer from '@/components/Footer';
import LoadingFallback from '@/components/LoadingFallback';
import SolutionsHero from './SolutionsHero';
import UseCasesSection from './UseCasesSection';
import FeaturesSection from './FeaturesSection';
import ClientsSection from './ClientsSection';
import IntegrationSection from './IntegrationSection';
import CTASection from './CTASection';

export default function Solutions() {
  return (
    <div className="min-h-screen bg-white">
      <Header />
      <main>
        <Suspense fallback={<LoadingFallback icon="ri-lightbulb-flash-line" />}>
          <SolutionsHero />
          <UseCasesSection />
          <FeaturesSection />
          <ClientsSection />
          <IntegrationSection />
          <CTASection />
        </Suspense>
      </main>
      <Footer />
    </div>
  );
}