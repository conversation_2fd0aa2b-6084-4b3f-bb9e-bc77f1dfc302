'use client';

export default function FeaturesSection() {
  const features = [
    {
      icon: 'ri-shield-check-line',
      title: '银行级安全',
      description: '采用多重加密技术，确保每笔交易的安全性和可靠性。'
    },
    {
      icon: 'ri-speed-line',
      title: '极速处理',
      description: '秒级确认，分钟级到账，为您的业务提供最快的支付体验。'
    },
    {
      icon: 'ri-global-line',
      title: '全球覆盖',
      description: '支持200+国家和地区，真正实现全球无界限支付。'
    },
    {
      icon: 'ri-code-s-slash-line',
      title: '简单集成',
      description: '提供完整的SDK和API文档，几行代码即可完成集成。'
    },
    {
      icon: 'ri-customer-service-line',
      title: '24/7支持',
      description: '专业技术团队全天候为您提供技术支持和客户服务。'
    },
    {
      icon: 'ri-line-chart-line',
      title: '实时监控',
      description: '提供详细的交易数据和分析报告，帮助优化业务决策。'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            核心优势
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            我们的解决方案具备以下核心优势，帮助您的业务快速增长
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-6 rounded-xl hover:bg-gray-50 transition-colors duration-300">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <i className={`${feature.icon} text-blue-600 text-2xl`}></i>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>
              <p className="text-gray-600">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}