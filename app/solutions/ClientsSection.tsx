'use client';

export default function ClientsSection() {
  const clients = [
    { name: 'TechCorp', logo: 'TC', industry: '科技' },
    { name: 'GlobalTrade', logo: 'GT', industry: '贸易' },
    { name: 'FinanceFirst', logo: 'FF', industry: '金融' },
    { name: 'GameStudio', logo: 'GS', industry: '游戏' },
    { name: 'MediaPlus', logo: 'MP', industry: '媒体' },
    { name: 'ShopCenter', logo: 'SC', industry: '电商' }
  ];

  const testimonials = [
    {
      quote: '集成CryptoPay后，我们的国际销售额增长了40%，客户满意度显著提升。',
      author: '张经理',
      company: 'TechCorp',
      role: '技术总监'
    },
    {
      quote: '支付处理速度快，手续费低，是我们跨境贸易的理想选择。',
      author: '李总',
      company: 'GlobalTrade',
      role: 'CEO'
    },
    {
      quote: '合规性强，安全可靠，完全满足我们金融业务的严格要求。',
      author: '王主管',
      company: 'FinanceFirst',
      role: '风控总监'
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            客户案例
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            全球领先企业的信任选择
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 mb-16">
          {clients.map((client, index) => (
            <div key={index} className="text-center">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-2">
                <span className="text-lg font-bold text-gray-600">{client.logo}</span>
              </div>
              <p className="text-sm font-medium text-gray-900">{client.name}</p>
              <p className="text-xs text-gray-500">{client.industry}</p>
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="bg-gray-50 rounded-xl p-6">
              <div className="mb-4">
                <i className="ri-double-quotes-l text-blue-600 text-2xl"></i>
              </div>
              <p className="text-gray-700 mb-4 italic">
                {testimonial.quote}
              </p>
              <div className="flex items-center">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
                  <span className="text-white font-medium text-sm">
                    {testimonial.author.charAt(0)}
                  </span>
                </div>
                <div>
                  <p className="font-medium text-gray-900">{testimonial.author}</p>
                  <p className="text-sm text-gray-600">{testimonial.role}, {testimonial.company}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}