'use client';

export default function IntegrationSection() {
  const integrationMethods = [
    {
      title: '支付小部件',
      description: '即插即用的支付组件，无需复杂开发，快速集成到您的网站。',
      icon: 'ri-widget-line',
      features: ['无需编程', '自定义界面', '响应式设计', '多语言支持']
    },
    {
      title: 'REST API',
      description: '完整的RESTful API，为开发者提供灵活的集成方案。',
      icon: 'ri-code-line',
      features: ['完整文档', '多种语言SDK', '实时回调', '测试环境']
    },
    {
      title: '托管结账',
      description: '安全的托管支付页面，简化您的开发流程，保障支付安全。',
      icon: 'ri-secure-payment-line',
      features: ['PCI合规', '一键支付', '风险控制', '移动优化']
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            集成方式
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            选择最适合您业务的集成方式，快速开启加密货币支付
          </p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {integrationMethods.map((method, index) => (
            <div key={index} className="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow duration-300">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <i className={`${method.icon} text-blue-600 text-2xl`}></i>
              </div>
              
              <h3 className="text-xl font-semibold text-gray-900 mb-4 text-center">
                {method.title}
              </h3>
              
              <p className="text-gray-600 mb-6 text-center">
                {method.description}
              </p>
              
              <ul className="space-y-3 mb-8">
                {method.features.map((feature, idx) => (
                  <li key={idx} className="flex items-center text-sm text-gray-600">
                    <i className="ri-check-line text-green-500 mr-2 w-4 h-4 flex items-center justify-center"></i>
                    {feature}
                  </li>
                ))}
              </ul>
              
              <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors duration-200 whitespace-nowrap cursor-pointer">
                开始集成
              </button>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}