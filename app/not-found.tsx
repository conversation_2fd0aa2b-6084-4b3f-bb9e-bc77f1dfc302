
'use client';

import Link from 'next/link';
import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function NotFound() {
  const quickLinks = [
    { href: '/', label: '返回首页', icon: 'ri-home-line' },
    { href: '/buy-crypto', label: '购买加密货币', icon: 'ri-shopping-cart-line' },
    { href: '/solutions', label: '解决方案', icon: 'ri-lightbulb-line' },
    { href: '/contact', label: '联系我们', icon: 'ri-customer-service-line' }
  ];

  const helpOptions = [
    {
      title: '搜索帮助',
      description: '在我们的帮助中心搜索答案',
      icon: 'ri-search-line',
      color: 'from-blue-500 to-blue-600'
    },
    {
      title: '在线客服',
      description: '与我们的客服团队实时沟通',
      icon: 'ri-message-3-line',
      color: 'from-green-500 to-green-600'
    },
    {
      title: '技术支持',
      description: '获取专业的技术支持服务',
      icon: 'ri-tools-line',
      color: 'from-purple-500 to-purple-600'
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Header />
      
      <main className="py-24">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          {/* 404 主要内容 */}
          <div className="mb-16">
            <div className="inline-flex items-center justify-center w-32 h-32 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-8 shadow-2xl">
              <span className="text-6xl font-bold text-white">404</span>
            </div>
            
            <h1 className="text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-6">
              页面未找到
            </h1>
            
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
              抱歉，您访问的页面不存在或已被移动。请检查URL地址是否正确，或使用下面的快速链接导航到其他页面。
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Link
                href="/"
                className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-8 rounded-xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap"
              >
                <i className="ri-home-line mr-2"></i>
                返回首页
              </Link>
              
              <button
                onClick={() => window.history.back()}
                className="inline-flex items-center border border-gray-300 text-gray-700 py-3 px-8 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 whitespace-nowrap"
              >
                <i className="ri-arrow-left-line mr-2"></i>
                返回上一页
              </button>
            </div>
          </div>
          
          {/* 快速链接 */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-800 bg-clip-text text-transparent mb-8">
              快速导航
            </h2>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {quickLinks.map((link, index) => (
                <Link
                  key={index}
                  href={link.href}
                  className="group p-6 bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:shadow-lg transition-shadow duration-300">
                    <i className={`${link.icon} text-white text-xl`}></i>
                  </div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">{link.label}</h3>
                </Link>
              ))}
            </div>
          </div>
          
          {/* 帮助选项 */}
          <div className="mb-16">
            <h2 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-blue-800 bg-clip-text text-transparent mb-8">
              需要帮助？
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {helpOptions.map((option, index) => (
                <div
                  key={index}
                  className="group p-8 bg-white/80 backdrop-blur-sm rounded-2xl border border-gray-100 shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 cursor-pointer"
                >
                  <div className={`w-16 h-16 bg-gradient-to-r ${option.color} rounded-2xl flex items-center justify-center mx-auto mb-6 group-hover:shadow-xl transition-shadow duration-300`}>
                    <i className={`${option.icon} text-white text-2xl`}></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">{option.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{option.description}</p>
                </div>
              ))}
            </div>
          </div>
          
          {/* 联系信息 */}
          <div className="bg-white/70 backdrop-blur-sm rounded-3xl p-10 border border-gray-100 shadow-2xl">
            <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-blue-800 bg-clip-text text-transparent mb-6">
              仍然找不到您要的内容？
            </h3>
            <p className="text-gray-600 text-lg mb-8 max-w-2xl mx-auto">
              我们的支持团队随时准备为您提供帮助。请通过以下方式联系我们：
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center bg-gradient-to-r from-blue-500 to-blue-600 text-white py-3 px-8 rounded-xl font-semibold shadow-xl hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 whitespace-nowrap"
              >
                <i className="ri-mail-line mr-2"></i>
                发送邮件
              </Link>
              
              <Link
                href="/support"
                className="inline-flex items-center border border-gray-300 text-gray-700 py-3 px-8 rounded-xl font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 whitespace-nowrap"
              >
                <i className="ri-customer-service-line mr-2"></i>
                在线客服
              </Link>
            </div>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  );
}
