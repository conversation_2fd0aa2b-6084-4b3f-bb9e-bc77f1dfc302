'use client';

import { useLanguage } from '../contexts/LanguageContext';

interface LoadingFallbackProps {
  icon?: string;
  message?: string;
  description?: string;
}

export default function LoadingFallback({ 
  icon = 'ri-home-4-line', 
  message,
  description 
}: LoadingFallbackProps) {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50">
      <div className="text-center">
        <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-3xl flex items-center justify-center mx-auto mb-6 animate-pulse shadow-2xl">
          <i className={`${icon} text-white text-3xl`}></i>
        </div>
        <div className="text-gray-600 font-medium text-lg">
          {message || t('common.loading')}
        </div>
        <div className="mt-4 flex justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
        {description && (
          <div className="text-sm text-gray-500 mt-4">{description}</div>
        )}
      </div>
    </div>
  );
}
