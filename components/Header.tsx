
'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useLanguage } from '../contexts/LanguageContext';
import LanguageSwitcher from './LanguageSwitcher';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { t } = useLanguage();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const navItems = [
    { name: t('navigation.home'), href: '/', icon: 'ri-home-4-line' },
    { name: t('navigation.solutions'), href: '/solutions', icon: 'ri-lightbulb-flash-line' },
    { name: t('navigation.pricing'), href: '/pricing', icon: 'ri-price-tag-3-line' },
    { name: t('navigation.buyCrypto'), href: '/buy-crypto', icon: 'ri-coin-line' },
    { name: t('navigation.developers'), href: '/developers', icon: 'ri-code-s-slash-line' },
    { name: t('navigation.compliance'), href: '/compliance', icon: 'ri-shield-star-line' },
    { name: t('navigation.support'), href: '/support', icon: 'ri-customer-service-2-line' },
    { name: t('navigation.contact'), href: '/contact', icon: 'ri-mail-send-line' }
  ];

  return (
    <header className={`fixed top-0 w-full z-50 transition-all duration-500 ${
      isScrolled 
        ? 'bg-white/90 backdrop-blur-2xl border-b border-gray-200/50 shadow-2xl shadow-gray-900/10' 
        : 'bg-white/80 backdrop-blur-xl border-b border-gray-200/30'
    }`}>
      <div className="mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-4 group">
              <div className="relative">
                <div className="w-14 h-14 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-3xl flex items-center justify-center shadow-xl shadow-blue-500/30 group-hover:shadow-2xl group-hover:shadow-blue-500/40 transition-all duration-500 group-hover:scale-110 group-hover:rotate-3">
                  <i className="ri-coin-line text-white text-2xl group-hover:scale-110 transition-transform duration-300"></i>
                </div>
                <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-400 to-indigo-400 rounded-3xl blur-xl opacity-0 group-hover:opacity-30 transition-opacity duration-500"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-3xl font-black bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 bg-clip-text text-transparent group-hover:from-blue-700 group-hover:via-purple-700 group-hover:to-indigo-700 transition-all duration-300" style={{ fontFamily: 'Pacifico, serif' }}>
                  CryptoPay
                </span>
                <span className="text-xs text-gray-500 uppercase tracking-wider font-medium">
                  {t('common.premium')}
                </span>
              </div>
            </Link>
          </div>
          
          <nav className="hidden lg:flex items-center space-x-1">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="group relative flex items-center space-x-3 text-gray-700 hover:text-blue-600 px-5 py-4 rounded-2xl text-sm font-semibold transition-all duration-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 hover:shadow-lg hover:shadow-blue-500/10 hover:scale-105"
              >
                <i className={`${item.icon} text-lg group-hover:scale-110 transition-transform duration-300`}></i>
                <span className="relative">
                  {item.name}
                  <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-blue-600 to-purple-600 transition-all duration-300 group-hover:w-full rounded-full"></span>
                </span>
              </Link>
            ))}
          </nav>

          <div className="hidden lg:flex items-center space-x-4">
            <LanguageSwitcher />
            <Link
              href="/login"
              className="group text-gray-700 hover:text-blue-600 px-6 py-3 text-sm font-semibold transition-all duration-300 flex items-center space-x-2 rounded-2xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 hover:shadow-lg hover:shadow-gray-500/10"
            >
              <i className="ri-user-3-line group-hover:scale-110 transition-transform duration-300"></i>
              <span>{t('common.login')}</span>
            </Link>
            <Link
              href="/dashboard"
              className="group relative bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white px-8 py-4 rounded-2xl text-sm font-bold transition-all duration-300 whitespace-nowrap cursor-pointer shadow-xl shadow-blue-500/30 hover:shadow-2xl hover:shadow-blue-500/40 transform hover:-translate-y-1 hover:scale-105 flex items-center space-x-2 overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-blue-700 via-purple-700 to-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <i className="ri-dashboard-3-line relative z-10 group-hover:scale-110 transition-transform duration-300"></i>
              <span className="relative z-10">{t('common.console')}</span>
            </Link>
          </div>

          <button
            className="lg:hidden p-4 rounded-2xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-300 hover:shadow-lg hover:shadow-gray-500/10"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <i className={`${isMenuOpen ? 'ri-close-line' : 'ri-menu-4-line'} text-2xl transition-all duration-300 ${isMenuOpen ? 'rotate-90 text-blue-600' : 'text-gray-700'}`}></i>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      <div className={`lg:hidden transition-all duration-500 ${isMenuOpen ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'} overflow-hidden`}>
        <div className="bg-white/95 backdrop-blur-2xl border-t border-gray-200/50 shadow-2xl">
          <div className="px-6 pt-6 pb-8 space-y-3">
            {navItems.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="flex items-center space-x-4 px-6 py-4 text-base font-semibold text-gray-700 hover:text-blue-600 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 rounded-2xl transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/10"
                onClick={() => setIsMenuOpen(false)}
              >
                <i className={`${item.icon} text-xl`}></i>
                <span>{item.name}</span>
              </Link>
            ))}
            <div className="border-t border-gray-200 pt-6 mt-6">
              <div className="mb-4 px-6">
                <LanguageSwitcher />
              </div>
              <Link
                href="/login"
                className="flex items-center space-x-4 px-6 py-4 text-base font-semibold text-gray-700 hover:text-blue-600 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 rounded-2xl transition-all duration-300 mb-3"
                onClick={() => setIsMenuOpen(false)}
              >
                <i className="ri-user-3-line text-xl"></i>
                <span>{t('common.login')}</span>
              </Link>
              <Link
                href="/dashboard"
                className="flex items-center space-x-4 px-6 py-4 text-base font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 text-white rounded-2xl mx-0 text-center shadow-xl shadow-blue-500/30 hover:shadow-2xl hover:shadow-blue-500/40 transition-all duration-300 hover:-translate-y-1 hover:scale-105"
                onClick={() => setIsMenuOpen(false)}
              >
                <i className="ri-dashboard-3-line text-xl"></i>
                <span>{t('common.console')}</span>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
